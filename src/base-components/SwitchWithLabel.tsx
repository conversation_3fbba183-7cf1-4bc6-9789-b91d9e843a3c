import {Switch} from 'react-native-paper';
import type {SwitchProps} from 'react-native-paper';
import {Box} from './Box';
import {Text} from './Text';

type SwitchWithLabelProps = SwitchProps & {
  label: string;
  previewValue?: string;
  right?: React.ReactNode;
  subLabel?: string | undefined;
};

export const SwitchWithLabel: React.FC<SwitchWithLabelProps> = ({
  label,
  previewValue,
  right,
  subLabel,
  ...rest
}) => (
  <Box alignItems='center' flexDirection='row' justifyContent='space-between' maxWidth='100%' my={1}>
    <Box flexDirection='column' flexShrink={1}>
      <Text variant='labelMedium'>{label}</Text>
      {subLabel && (
        <Text flexWrap='wrap' pl={3} variant='bodySmall'>
          {subLabel}
        </Text>
      )}
    </Box>
    <Box alignItems='center' flexDirection='row'>
      {right}
      <Switch {...rest} />
      {previewValue && (
        <Text flexWrap='wrap' pl={1} variant='labelMedium'>
          {previewValue}
        </Text>
      )}
    </Box>
  </Box>
);
