import React from 'react';
import {Box} from './Box';
import {Text} from './Text';

type ComponentErrorBoundaryState = {
  hasError: boolean;
  error?: Error;
};

type ComponentErrorBoundaryProps = {
  children: React.ReactNode;
  componentName?: string;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
};

export class ComponentErrorBoundary extends React.Component<
  ComponentErrorBoundaryProps,
  ComponentErrorBoundaryState
> {
  constructor(props: ComponentErrorBoundaryProps) {
    super(props);
    this.state = {hasError: false};
  }

  static getDerivedStateFromError(error: Error): ComponentErrorBoundaryState {
    return {hasError: true, error};
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error in ${this.props.componentName || 'Component'}:`, error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <Box
          style={{
            padding: 16,
            backgroundColor: '#fff3cd',
            borderRadius: 8,
            borderWidth: 1,
            borderColor: '#ffeaa7',
          }}
        >
          <Text style={{color: '#856404', fontWeight: 'bold'}}>
            {this.props.componentName || 'Component'} Error
          </Text>
          <Text style={{color: '#856404', marginTop: 4}}>
            Something went wrong. Please try again.
          </Text>
          {__DEV__ && this.state.error && (
            <Text style={{color: '#856404', marginTop: 8, fontSize: 12}}>
              {this.state.error.message}
            </Text>
          )}
        </Box>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string,
  fallback?: React.ReactNode,
) => {
  const WrappedComponent = (props: P) => (
    <ComponentErrorBoundary componentName={componentName} fallback={fallback}>
      <Component {...props} />
    </ComponentErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
};
