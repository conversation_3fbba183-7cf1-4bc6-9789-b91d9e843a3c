import Animated from 'react-native-reanimated';

export {FlashList} from '@shopify/flash-list';
export {View as ViewUseBoxInstead} from 'react-native';
export {
  CircularProgressBase,
  default as CircularProgress,
} from 'react-native-circular-progress-indicator';
export {TouchableOpacity} from 'react-native-gesture-handler';
export {default as Swipeable} from 'react-native-gesture-handler/Swipeable';
export {
  Checkbox,
  Chip,
  Drawer as PaperDrawer,
  IconButton,
  List,
  Snackbar,
  ToggleButton,
} from 'react-native-paper';
export {DatePickerModal, TimePickerModal} from 'react-native-paper-dates';
export {StatusBar} from 'expo-status-bar';
export * from './Accordion';
export * from './AnimatedTextNumber';
export * from './Box';
export * from './Button';
export * from './ComponentErrorBoundary';
export * from './ConditionalRender';
export * from './ConditionalWrapper';
export * from './Confetti';
export * from './ContextMenu';
export * from './Divider';
export * from './DropDown';
export * from './Grid';
export * from './Icon';
export {KeyboardAwareScrollView} from './KeyboardAwareScrollView';
export * from './LoaderSpinner';
export * from './LoaderWrapper';
export * from './LoadingIndicator';
export * from './LoadingOverlay';
export * from './MultiSelect';
export * from './PhotoListInput';
export * from './PressableButton';
export * from './RadioButton';
export * from './ScaledPressable';
export * from './SearchBar';
export * from './SegmentedButtons';
export * from './ShinySkeletonView';
export * from './StickyAboveNavigation';
export * from './Surface';
export * from './SuspenseWrapper';
export * from './SwipeableRow';
export * from './Switch';
export * from './SwitchWithLabel';
export * from './Text';
export * from './TextInput';
export * from './TextInputBare';
export * from './TextLink';
export * from './ThemeOverrideWrapper';
export * from './TimePicker';
export * from './TouchableHighlight';
export * from './TouchableHighlightLong';
export * from './Unmount';

// eslint-disable-next-line @typescript-eslint/naming-convention -- re-export
export const AnimatedView = Animated.View;
