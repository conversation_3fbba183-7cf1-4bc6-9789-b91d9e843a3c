import {useCallback, useMemo, useRef, useState} from 'react';
import {Chip, Icon} from 'react-native-paper';
import {MultiSelect as MultiSelectLibrary} from 'react-native-element-dropdown';
import type {IMultiSelectRef} from 'react-native-element-dropdown';
import {useAppTheme} from '@utils';
import {Box} from './Box';
import {Text} from './Text';
import {TextInputPaper} from './TextInput';
import {withErrorBoundary} from './ComponentErrorBoundary';

type SelectItem<T = string> = {
  label: string;
  value: T;
};

type MultiSelectProps<T = string> = {
  accessibilityLabel?: string;
  isDisabled?: boolean;
  isSearch?: boolean;
  onChange: (value: T[]) => void;
  options: SelectItem<T>[];
  placeholder?: string;
  value?: T[];
};

const CloseIcon = () => (
  <Icon color='#fff' size={20} source='close' />
);

const MultiSelectInternal = <T = string,>({
  accessibilityLabel,
  isDisabled = false,
  isSearch = false,
  onChange,
  options,
  placeholder,
  value,
}: MultiSelectProps<T>) => {
  const [isFocused, setIsFocused] = useState(false);
  const theme = useAppTheme();
  const ref = useRef<IMultiSelectRef>(null);

  // Memoize the selected values set for performance
  const selectedValuesSet = useMemo(() => new Set(value ?? []), [value]);

  const isItemSelected = useCallback(
    (item: SelectItem<T>) => selectedValuesSet.has(item.value),
    [selectedValuesSet],
  );

  // Memoize styles to prevent recreation on every render
  const itemContainerStyle = useMemo(
    () => ({
      padding: 17,
      flexDirection: 'row' as const,
      justifyContent: 'space-between' as const,
      alignItems: 'center' as const,
    }),
    [],
  );

  const textStyle = useMemo(() => ({flex: 1, ...theme.fonts.labelMedium}), [theme.fonts.labelMedium]);

  const renderItem = useCallback(
    (item: SelectItem<T>) => (
      <Box style={itemContainerStyle}>
        <Text style={textStyle}>{item.label}</Text>
        {isItemSelected(item) && (
          <Text accessibilityLabel='Selected' mr={1}>
            ✅
          </Text>
        )}
      </Box>
    ),
    [isItemSelected, itemContainerStyle, textStyle],
  );

  const renderRightIcon = () => (
    <Box style={{position: 'absolute', right: 30, top: -2}}>
      <TextInputPaper.Icon
        color={theme.colors.dodgerBlueButtonBackground}
        icon={`arrow-${isFocused ? 'up' : 'down'}-drop-circle-outline`}
        onPress={() => ref.current?.open()}
      />
    </Box>
  );

  const renderSelectedItem = (item: SelectItem, unSelect?: (item: SelectItem) => void) => (
    <Chip
      closeIcon={CloseIcon}
      mode='outlined'
      showSelectedCheck={false}
      style={{
        marginRight: 6,
        marginTop: 8,
        paddingLeft: 0,
        borderRadius: 14,
        borderWidth: 0,
        backgroundColor: theme.colors.dodgerBlueButtonBackground,
      }}
      textStyle={{color: '#fff', textAlignVertical: 'center'}}
      onClose={() => unSelect?.(item)}
    >
      {item.label}
    </Chip>
  );

  // Memoize container styles
  const containerStyle = useMemo(() => ({borderRadius: 10}), []);
  const itemContainerStyleInternal = useMemo(() => ({borderRadius: 10}), []);
  const placeholderStyle = useMemo(
    () => ({
      color: theme.colors.secondaryContainer2,
      fontSize: theme.fonts.labelMedium.fontSize,
    }),
    [theme.colors.secondaryContainer2, theme.fonts.labelMedium.fontSize],
  );
  const selectedStyle = useMemo(
    () => ({
      flexDirection: 'row' as const,
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      borderRadius: 14,
      marginTop: 8,
      marginRight: 12,
      paddingHorizontal: 8,
      paddingVertical: 8,
    }),
    [],
  );
  const selectedTextStyle = useMemo(
    () => ({color: theme.colors.primary, ...theme.fonts.labelMedium}),
    [theme.colors.primary, theme.fonts.labelMedium],
  );
  const mainStyle = useMemo(
    () => ({
      borderRadius: 10,
      paddingVertical: 12,
      paddingHorizontal: 10,
      borderColor: theme.colors.cardBorderColor,
      borderWidth: 0,
      backgroundColor: '#fff',
      ...theme.fonts.labelMedium,
    }),
    [theme.colors.cardBorderColor, theme.fonts.labelMedium],
  );

  const defaultAccessibilityLabel = accessibilityLabel ?? 'Multi-select dropdown';

  // Error handling for invalid options
  if (!options || options.length === 0) {
    console.warn('MultiSelect: No options provided');
  }

  return (
    <MultiSelectLibrary
      ref={ref}
      accessibilityLabel={defaultAccessibilityLabel}
      containerStyle={containerStyle}
      data={options}
      disable={isDisabled}
      fontFamily={theme.fonts.labelMedium.fontFamily}
      iconStyle={{}}
      itemContainerStyle={itemContainerStyleInternal}
      labelField='label'
      placeholderStyle={placeholderStyle}
      renderItem={renderItem}
      renderRightIcon={renderRightIcon}
      renderSelectedItem={renderSelectedItem}
      search={isSearch}
      searchPlaceholder='Search...'
      selectedStyle={selectedStyle}
      selectedTextStyle={selectedTextStyle}
      style={mainStyle}
      value={value as string[]} // Type assertion for library compatibility
      valueField='value'
      onBlur={() => setIsFocused(false)}
      onChange={onChange as (value: string[]) => void} // Type assertion for library compatibility
      onFocus={() => setIsFocused(true)}
      {...(placeholder && {placeholder})}
    />
  );
};

// Export the component wrapped with error boundary
export const MultiSelect = withErrorBoundary(MultiSelectInternal, 'MultiSelect');
