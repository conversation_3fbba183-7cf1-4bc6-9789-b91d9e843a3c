import {useRef, useState} from 'react';
import {Dropdown as DropdownInternal} from 'react-native-element-dropdown';
import type {IDropdownRef} from 'react-native-element-dropdown';
import {useAppTheme} from '@utils';
import {Box} from './Box';
import {Text} from './Text';
import {TextInputPaper} from './TextInput';

type Option = {
  label: string;
  value: string;
};

type LabelOptions = {
  defaultLabel: number;
  end: number;
  formatLabel?: (key: number) => string;
  interval: number;
  start: number;
  transformValue: (value: number) => string;
};

export const generateDropdownOptions = (options: LabelOptions): Option[] => {
  const {defaultLabel, end, formatLabel, interval, start, transformValue} = options;

  return Array.from(
    {length: Math.floor((end - start) / interval) + 1},
    (_, i) => start + (i * interval),
  ).map(currentValue => {
    const label = `${formatLabel ? formatLabel(currentValue) : currentValue} ${currentValue === defaultLabel ? '(default)' : ''}`;
    const value = transformValue(currentValue);

    return {label, value};
  });
};

type DropDownSelfContainedProps = Pick<React.ComponentProps<typeof DropdownInternal>, 'onChange' | 'placeholder'> & {
  isDisabled?: boolean | undefined;
  isSearch?: boolean | undefined;
  label: string;
  options: Option[];
  value: string | undefined;
};

export const DropDownSelfContained: React.FC<DropDownSelfContainedProps> = ({
  isDisabled = false,
  isSearch = false,
  label,
  onChange,
  options,
  placeholder,
  value,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const theme = useAppTheme();
  const ref = useRef<IDropdownRef>(null);

  const isItemSelected = (item: Option) =>
    item.value === value;

  const renderItem = (item: Option) => (
    <Box
      style={{
        padding: 17,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Text style={{flex: 1, ...theme.fonts.labelMedium}}>{item.label}</Text>
      {isItemSelected(item) && <Text mr={1}>✅</Text>}
    </Box>
  );

  const renderRightIcon = () => (
    <Box style={{position: 'absolute', right: 30, top: -2}}>
      <TextInputPaper.Icon
        color={theme.colors.dodgerBlueButtonBackground}
        icon={`arrow-${isFocused ? 'up' : 'down'}-drop-circle-outline`}
        onPress={() => ref.current?.open()}
      />
    </Box>
  );

  return (
    <Box position='relative'>
      <Text style={{position: 'absolute', top: -7, left: 10, zIndex: 1, color: theme.colors.secondaryContainer2}} variant='labelSmall'>{label}</Text>
      <DropdownInternal
        ref={ref}
        data={options}
        disable={isDisabled}
        iconStyle={{}}
        labelField='label'
        {...(placeholder && {placeholder})}
        containerStyle={{borderRadius: 10}}
        fontFamily={theme.fonts.labelMedium.fontFamily}
        itemContainerStyle={{borderRadius: 10}}
        placeholderStyle={{
          color: theme.colors.secondaryContainer2,
          fontSize: theme.fonts.labelMedium.fontSize,
        }}
        renderItem={renderItem}
        renderRightIcon={renderRightIcon}
        search={isSearch}
        searchPlaceholder='Search...'
        selectedTextStyle={{color: theme.colors.primary, ...theme.fonts.labelMedium}}
        style={{
          borderRadius: 10,
          paddingVertical: 12,
          paddingHorizontal: 10,
          borderColor: theme.colors.cardBorderColor,
          borderWidth: 0,
          backgroundColor: '#fff',
          ...theme.fonts.labelMedium,
        }}
        value={value}
        valueField='value'
        onBlur={() => setIsFocused(false)}
        onChange={onChange}
        onFocus={() => setIsFocused(true)}
      />
    </Box>
  );
};
