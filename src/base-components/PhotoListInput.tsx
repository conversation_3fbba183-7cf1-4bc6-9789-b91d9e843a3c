import {I<PERSON><PERSON>utton} from 'react-native-paper';
import {Images, type SourcedImageProps} from '@assets';
import {useMultiImagePickerState} from '@data-hooks';
import type {ImageUrl, Initializer} from '@types';
import {useAppTheme} from '@utils';
import {Box} from './Box';
import {ButtonOutlined} from './Button';
import {Text} from './Text';

type PhotoListInputProps = {
  addLabel?: string;
  imageProps?: SourcedImageProps;
  items: string[] | undefined;
  onChange: (value: Initializer<ImageUrl[] | undefined>) => void;
  removeLabel?: string;
  storagePathPrefix: `${string}/`;
};

export const PhotoListInput: React.FC<PhotoListInputProps> = ({
  addLabel = 'Add image',
  imageProps,
  items = [],
  onChange,
  storagePathPrefix,
}) => {
  const theme = useAppTheme();
  const imagePickerMutation = useMultiImagePickerState(storagePathPrefix, onChange);

  const handleAdd = () => imagePickerMutation.mutateAsync();

  const handleRemove = (index: number) => {
    onChange(prev => (prev ? prev.filter((_, i) => i !== index) : prev));
  };

  return (
    <>
      <Box flexDirection='row' justifyContent='flex-start' pb={1}>
        <ButtonOutlined icon='file-image-plus-outline' onPress={handleAdd}>
          {addLabel}
        </ButtonOutlined>
      </Box>

      {imagePickerMutation.isPending && <Text pl={1}>Loading...</Text>}
      {!imagePickerMutation.isPending && (
        <>
          {items.map((item, index) => (
            <Box key={`photo-list-photo-${item}`} flex={1} pb={1} px={1}>
              <Box display='inline-block' position='relative'>
                <Images.placeholderWorkoutImage overrideSource={item} {...imageProps} />
                <IconButton
                  icon='delete'
                  iconColor={theme.colors.delete}
                  mode='contained'
                  style={{
                    position: 'absolute',
                    top: 7,
                  }}
                  onPress={() => handleRemove(index)}
                />
              </Box>
            </Box>
          ))}
        </>
      )}
    </>
  );
};
