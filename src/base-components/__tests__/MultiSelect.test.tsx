import {fireEvent, render, screen} from '@testing-library/react-native';
import {MultiSelect} from '../MultiSelect';

// Mock the external library
jest.mock('react-native-element-dropdown', () => ({
  MultiSelect: jest.fn(({data, renderItem, onChange, value}) => {
    return (
      <div data-testid="multiselect-internal">
        {data?.map((item: any) => (
          <div
            key={item.value}
            data-testid={`option-${item.value}`}
            onPress={() => {
              const newValue = value?.includes(item.value) 
                ? value.filter((v: any) => v !== item.value)
                : [...(value || []), item.value];
              onChange?.(newValue);
            }}
          >
            {renderItem?.(item)}
          </div>
        ))}
      </div>
    );
  }),
}));

describe('MultiSelect', () => {
  const mockOptions = [
    {label: 'Option 1', value: 'opt1'},
    {label: 'Option 2', value: 'opt2'},
    {label: 'Option 3', value: 'opt3'},
  ];

  const defaultProps = {
    options: mockOptions,
    onChange: jest.fn(),
    value: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with options', () => {
      render(<MultiSelect {...defaultProps} />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
      expect(screen.getByTestId('option-opt1')).toBeTruthy();
      expect(screen.getByTestId('option-opt2')).toBeTruthy();
      expect(screen.getByTestId('option-opt3')).toBeTruthy();
    });

    it('should render with placeholder', () => {
      render(<MultiSelect {...defaultProps} placeholder="Select options" />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });

    it('should render with custom accessibility label', () => {
      render(<MultiSelect {...defaultProps} accessibilityLabel="Custom multiselect" />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });
  });

  describe('Selection behavior', () => {
    it('should show selected items with checkmark', () => {
      render(<MultiSelect {...defaultProps} value={['opt1']} />);
      
      // The selected item should be rendered with a checkmark
      expect(screen.getByTestId('option-opt1')).toBeTruthy();
    });

    it('should handle multiple selections', () => {
      render(<MultiSelect {...defaultProps} value={['opt1', 'opt2']} />);
      
      expect(screen.getByTestId('option-opt1')).toBeTruthy();
      expect(screen.getByTestId('option-opt2')).toBeTruthy();
    });

    it('should handle empty selection', () => {
      render(<MultiSelect {...defaultProps} value={[]} />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });
  });

  describe('Interaction', () => {
    it('should call onChange when selection changes', () => {
      const mockOnChange = jest.fn();
      render(<MultiSelect {...defaultProps} onChange={mockOnChange} />);
      
      const option = screen.getByTestId('option-opt1');
      fireEvent.press(option);
      
      expect(mockOnChange).toHaveBeenCalledWith(['opt1']);
    });

    it('should handle disabled state', () => {
      render(<MultiSelect {...defaultProps} isDisabled />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });
  });

  describe('Error handling', () => {
    it('should handle empty options array', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      render(<MultiSelect {...defaultProps} options={[]} />);
      
      expect(consoleSpy).toHaveBeenCalledWith('MultiSelect: No options provided');
      consoleSpy.mockRestore();
    });

    it('should handle undefined options', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      render(<MultiSelect {...defaultProps} options={undefined as any} />);
      
      expect(consoleSpy).toHaveBeenCalledWith('MultiSelect: No options provided');
      consoleSpy.mockRestore();
    });

    it('should handle missing onChange gracefully', () => {
      const props = {...defaultProps};
      delete (props as any).onChange;
      
      expect(() => render(<MultiSelect {...props} />)).not.toThrow();
    });
  });

  describe('Performance', () => {
    it('should handle large option lists', () => {
      const largeOptions = Array.from({length: 1000}, (_, i) => ({
        label: `Option ${i}`,
        value: `opt${i}`,
      }));

      expect(() => 
        render(<MultiSelect {...defaultProps} options={largeOptions} />)
      ).not.toThrow();
    });
  });

  describe('Type safety', () => {
    it('should work with string values', () => {
      render(<MultiSelect<string> {...defaultProps} />);
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });

    it('should work with number values', () => {
      const numberOptions = [
        {label: 'One', value: 1},
        {label: 'Two', value: 2},
      ];

      render(
        <MultiSelect<number>
          options={numberOptions}
          value={[1]}
          onChange={jest.fn()}
        />
      );
      
      expect(screen.getByTestId('multiselect-internal')).toBeTruthy();
    });
  });
});
