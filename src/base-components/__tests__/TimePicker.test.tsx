import {fireEvent, render, screen} from '@testing-library/react-native';
import {TimePicker} from '../TimePicker';

// Mock the ActionSheet component
jest.mock('react-native-actions-sheet', () => ({
  __esModule: true,
  default: jest.fn(({children}) => children),
  useScrollHandlers: () => ({simultaneousHandlers: []}),
}));

// Mock the Picker component
jest.mock('@react-native-picker/picker', () => ({
  Picker: jest.fn(({children}) => children),
}));

// Mock the gesture handler
jest.mock('react-native-gesture-handler', () => ({
  NativeViewGestureHandler: jest.fn(({children}) => children),
}));

describe('TimePicker', () => {
  const defaultProps = {
    label: 'Duration',
    value: {hours: 2, minutes: 30},
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with correct display value', () => {
      render(<TimePicker {...defaultProps} />);
      
      expect(screen.getByDisplayValue('2h, 30m')).toBeTruthy();
    });

    it('should render with correct label', () => {
      render(<TimePicker {...defaultProps} />);
      
      expect(screen.getByText('Duration')).toBeTruthy();
    });

    it('should render with default accessibility attributes', () => {
      render(<TimePicker {...defaultProps} />);
      
      const input = screen.getByDisplayValue('2h, 30m');
      expect(input.props.accessibilityLabel).toBe('Duration time picker');
      expect(input.props.accessibilityHint).toBe('Tap to open time selection wheel');
      expect(input.props.accessibilityRole).toBe('button');
    });

    it('should render with custom accessibility attributes', () => {
      render(
        <TimePicker
          {...defaultProps}
          accessibilityLabel="Custom time picker"
          accessibilityHint="Custom hint"
        />
      );
      
      const input = screen.getByDisplayValue('2h, 30m');
      expect(input.props.accessibilityLabel).toBe('Custom time picker');
      expect(input.props.accessibilityHint).toBe('Custom hint');
    });
  });

  describe('Interaction', () => {
    it('should call onPress when not disabled', () => {
      const mockOnChange = jest.fn();
      render(<TimePicker {...defaultProps} onChange={mockOnChange} />);
      
      const input = screen.getByDisplayValue('2h, 30m');
      fireEvent.press(input);
      
      // Since we're mocking ActionSheet, we can't test the actual opening
      // but we can verify the input is pressable
      expect(input.props.editable).toBe(false);
    });

    it('should not be pressable when disabled', () => {
      render(<TimePicker {...defaultProps} isDisabled />);
      
      const input = screen.getByDisplayValue('2h, 30m');
      expect(input.props.editable).toBe(false);
    });
  });

  describe('Time formatting', () => {
    it('should format single digit hours and minutes correctly', () => {
      render(<TimePicker {...defaultProps} value={{hours: 1, minutes: 5}} />);
      
      expect(screen.getByDisplayValue('1h, 5m')).toBeTruthy();
    });

    it('should format zero values correctly', () => {
      render(<TimePicker {...defaultProps} value={{hours: 0, minutes: 0}} />);
      
      expect(screen.getByDisplayValue('0h, 0m')).toBeTruthy();
    });

    it('should format maximum values correctly', () => {
      render(<TimePicker {...defaultProps} value={{hours: 23, minutes: 59}} />);
      
      expect(screen.getByDisplayValue('23h, 59m')).toBeTruthy();
    });
  });

  describe('Error handling', () => {
    it('should handle missing onChange gracefully', () => {
      const props = {...defaultProps};
      delete (props as any).onChange;
      
      expect(() => render(<TimePicker {...props} />)).not.toThrow();
    });

    it('should handle invalid time values gracefully', () => {
      expect(() => 
        render(<TimePicker {...defaultProps} value={{hours: -1, minutes: 70}} />)
      ).not.toThrow();
    });
  });
});
