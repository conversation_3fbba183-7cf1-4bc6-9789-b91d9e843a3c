/* eslint-disable unicorn/filename-case -- must be function that returns JSX instead of a component */
import {SafeAreaProviderCompat} from '@react-navigation/elements';
import {auth} from '@backend';
import {ButtonContained, Grid, Text} from '@base-components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS, DOMAIN_CONSTANTS} from '@constants';
import {useToggleState} from '@hooks';
import {reloadApp} from '@utils';
import {ScreenWrapper} from './ScreenWrapper';

type ReloadButtonProps = {
  onReload?: () => void;
};

export const ReloadButton: React.FC<ReloadButtonProps> = ({onReload}) => (
  <ButtonContained
    icon='refresh'
    onPress={async () => {
      onReload?.();
      await reloadApp();
    }}
  >
    {CONTENT_CODES().ERROR.RELOAD}
  </ButtonContained>
);

const ErrorButton: React.FC = () => {
  const [shouldThrowError, toggleShouldThrowError] = useToggleState(false);
  if (shouldThrowError) throw new Error('Intentionally threw error');

  return (
    <ButtonContained icon='alert-circle' mb={1} onPress={toggleShouldThrowError}>
      {CONTENT_CODES().ERROR.THROW_ERROR}
    </ButtonContained>
  );
};

export const getErrorScreen = (error: string, onReload: () => void) => (
  <SafeAreaProviderCompat>
    <ScreenWrapper>
      <Grid container alignItems='center' my={2}>
        <Grid center item mb={1} xs={12}>
          <Text variant='titleLarge'>{CONTENT_CODES().ERROR.TITLE}</Text>
        </Grid>
        <Grid center item mb={1} xs={12}>
          <Text>{CONTENT_CODES().ERROR.DESCRIPTION}</Text>
        </Grid>
        {error && (
          <Grid center item mb={1} xs={12}>
            <Text variant='bodySmall'>{error}</Text>
          </Grid>
        )}
        <Grid center item mt={2} xs={12}>
          <ReloadButton onReload={onReload} />
        </Grid>
        {DEV_FEATURE_FLAGS().isDebugViewEnabled ||
          (DOMAIN_CONSTANTS().SPECIAL_USERS.DEBUG_USERS.includes(auth.currentUser?.email ?? '') && (
            <Grid center item mt={2} xs={12}>
              <ErrorButton />
            </Grid>
          ))}
      </Grid>
    </ScreenWrapper>
  </SafeAreaProviderCompat>
);
/* eslint-enable unicorn/filename-case */
