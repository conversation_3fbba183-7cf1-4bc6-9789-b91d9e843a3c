import {DumbbellIcon, DurationIcon, GroupIcon} from '@assets';
import {Box, ButtonOutlined, LoaderWrapper, Text} from '@base-components';
import {ClientSearch, CoachStatBox, ScreenContent, ScreenHeader} from '@components';
import {CONTENT_CODES} from '@constants';
import {ScreenWrapper, useAppUserSafe} from '@contexts';
import {useTrainerClients} from '@data-hooks';
import {useHapticCallback} from '@hooks';
import {type CoachHomeScreenProps, useLinkTo} from '@navigation';
import {formatMillisecondsToHours, isNonEmptyArray, useAppTheme} from '@utils';

export const CoachHomeScreen: React.FC<CoachHomeScreenProps> = () => {
  const theme = useAppTheme();
  const {id} = useAppUserSafe();
  const {data, isPending, refetch} = useTrainerClients(id);
  const onRefresh = useHapticCallback(refetch);
  const to = useLinkTo();

  return (
    <ScreenWrapper onRefresh={onRefresh}>
      <ScreenHeader
        flexCenter={6}
        flexLeft={1}
        flexRight={1}
        left={<Box />}
        title={CONTENT_CODES().COACH.HOME.HEADER_TITLE}
      />

      <ScreenContent disableXPadding>
        <Text pb={1} px={2} variant='headlineMedium'>
          {CONTENT_CODES().COACH.HOME.SUMMARY_HEADER}
        </Text>

        <LoaderWrapper isLoading={!data || isPending} minHeight={100}>
          {data && (
            <Box px={2}>
              <CoachStatBox
                icon={<GroupIcon fill={theme.colors.dodgerBlue} />}
                label={CONTENT_CODES().COACH.SUMMARY.NUM_OF_CLIENTS}
                value={`${data.clients.length}`}
              />
              <CoachStatBox
                icon={<DumbbellIcon fill={theme.colors.dodgerBlue} />}
                label={CONTENT_CODES().COACH.SUMMARY.NUM_OF_WORKOUTS}
                value={`${data.totalNumberOfWorkouts}`}
              />
              <CoachStatBox
                icon={<DurationIcon fill={theme.colors.dodgerBlue} />}
                label={CONTENT_CODES().COACH.SUMMARY.DURATION}
                value={`${formatMillisecondsToHours(data.totalDurationOfWorkoutsInMs)}`}
              />
            </Box>
          )}
        </LoaderWrapper>

        <Box
          alignItems='center'
          flexDirection='row'
          justifyContent='space-between'
          pb={1}
          pt={2}
          px={2}
        >
          <Text variant='headlineMedium'>{CONTENT_CODES().COACH.HOME.CLIENTS_HEADER}</Text>
          <ButtonOutlined
            icon='plus'
            onPress={() => to.createUserCoach({organizationSearch: ''})}
          >
            {CONTENT_CODES().COACH.HOME.CREATE_USER_LABEL}
          </ButtonOutlined>
        </Box>

        <LoaderWrapper isLoading={isPending}>
          {!data && <Text>No clients found</Text>}
          {data && isNonEmptyArray(data.clients) && (
            <ClientSearch clients={data.clients} searchParamKey='clientSearchTerm' />
          )}
        </LoaderWrapper>
      </ScreenContent>
    </ScreenWrapper>
  );
};
