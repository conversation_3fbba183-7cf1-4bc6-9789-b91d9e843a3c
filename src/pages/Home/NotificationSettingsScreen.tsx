import {Box, ButtonOutlined, LoadingIndicator, SwitchWithLabel, Text} from '@base-components';
import {
  ChallengeNotificationSettings,
  DebugNotificationsBox,
  ScreenContent,
  ScreenHeader,
  StreakNotificationSettings,
  WellnessBlogSettings,
  WellnessQuizSettings,
  WorkoutNotificationSettings,
} from '@components';
import {CONTENT_CODES, isIos} from '@constants';
import {
  ScreenWrapper,
  useIsNotificationPermissionsEnabledNative,
  useIsNotificationSettingsDefault,
  useIsPushNotificationEnabled,
  useResetNotificationSettings,
  useToggleIsNotificationEnabled,
} from '@contexts';
import {onLinkToNativeNotificationSettings} from '@navigation';

export const NotificationSettingsScreen: React.FC = () => {
  const isNotificationPermissionsEnabled = useIsNotificationPermissionsEnabledNative();
  const isNotificationsEnabled = useIsPushNotificationEnabled();
  const toggleNotificationEnabled = useToggleIsNotificationEnabled();
  const reset = useResetNotificationSettings();
  const isDefaultSettings = useIsNotificationSettingsDefault();

  return (
    <ScreenWrapper>
      <ScreenHeader title={CONTENT_CODES().NOTIFICATIONS.SETTINGS.TITLE} />

      <ScreenContent>
        {isNotificationsEnabled === undefined && <LoadingIndicator />}

        {isNotificationsEnabled !== undefined && (
          <>
            <DebugNotificationsBox />

            <Box>
              <SwitchWithLabel
                label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.ENABLE_LABEL}
                value={isNotificationsEnabled}
                onValueChange={toggleNotificationEnabled}
              />
            </Box>

            {isNotificationPermissionsEnabled === false && (
              <>
                <Text variant='bodyMedium'>
                  {CONTENT_CODES().NOTIFICATIONS.SETTINGS.MUST_ENABLE_NATIVE}
                </Text>

                <ButtonOutlined
                  icon={isIos ? 'apple' : 'android'}
                  mb={2}
                  mt={1}
                  onPress={onLinkToNativeNotificationSettings}
                >
                  {CONTENT_CODES().NOTIFICATIONS.SETTINGS.OPEN_NATIVE_SETTINGS}
                </ButtonOutlined>
              </>
            )}

            <Text variant='bodySmall'>{CONTENT_CODES().NOTIFICATIONS.SETTINGS.DESCRIPTION}</Text>

            {isNotificationsEnabled && (
              <Box>
                <Box pt={2} />

                <Text variant='headlineMedium'>
                  {CONTENT_CODES().NOTIFICATIONS.SETTINGS.PUSH_NOTIFICATION_SECTION}
                </Text>

                <WorkoutNotificationSettings />

                <ChallengeNotificationSettings />

                <StreakNotificationSettings />

                <Box pt={2} />

                <Text variant='headlineMedium'>
                  {CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION_SECTION}
                </Text>

                <WellnessQuizSettings />

                <WellnessBlogSettings />

                <Text my={2} variant='bodySmall'>
                  {CONTENT_CODES().NOTIFICATIONS.SETTINGS.SAVED_AUTOMATICALLY}
                </Text>

                {!isDefaultSettings && (
                  <Box mx='auto'>
                    <ButtonOutlined icon='arrow-u-left-top' onPress={reset}>
                      {CONTENT_CODES().NOTIFICATIONS.SETTINGS.RESET}
                    </ButtonOutlined>
                  </Box>
                )}

                <Box pt={4} />
              </Box>
            )}
          </>
        )}
      </ScreenContent>
    </ScreenWrapper>
  );
};
