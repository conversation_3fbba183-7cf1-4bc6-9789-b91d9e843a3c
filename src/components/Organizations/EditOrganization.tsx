import {useMemo, useState} from 'react';
import {Accordion, Box, ButtonContained, Icon, RadioButton, Switch, Text, TextInput} from '@base-components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS} from '@constants';
import {useIsAdmin, useOrganizationState} from '@contexts';
import {useEmptyArray} from '@hooks';
import {
  allOrganizationTypes,
  getOrganizationTypeIcon,
  getOrganizationTypeLabel,
  type Organization,
  type UUIDString,
} from '@types';
import {CopyClipboardWrapper} from '../CopyClipboardButton';
import {SearchAndAddUsers} from '../Participants';
import {SearchAndAddOrganizations} from './SearchAndAddOrganizations';

type EditOrganizationProps = {
  initialState: Organization;
  isFirstCreate?: true;
  onSubmitSuccess: () => void;
  submitText?: string;
};

// eslint-disable-next-line max-lines-per-function -- form component
export const EditOrganization: React.FC<EditOrganizationProps> = ({
  initialState,
  isFirstCreate = false,
  onSubmitSuccess,
  submitText,
}) => {
  const {
    isChanged,
    isPendingSubmit,
    onAddAdmin,
    onAddClient,
    onAddCoach,
    onChangeParentOrganization,
    onNameChange,
    onRemoveAdmin,
    onRemoveClient,
    onRemoveCoach,
    onSubmit,
    onToggleIsHideChallenges,
    onTypeChange,
    organization,
  } = useOrganizationState(initialState, onSubmitSuccess);
  const isErrorName = !organization.name;
  const isErrorAdmins = organization.adminIds.length === 0;
  const isError = isErrorName || isErrorAdmins;

  const [hasNameChanged, setHasNameChanged] = useState(false);
  const emptyArray = useEmptyArray<UUIDString>();
  const parentOrganizationIdArray = useMemo(
    () => (organization.parentOrganizationId ? [organization.parentOrganizationId] : emptyArray),
    [emptyArray, organization.parentOrganizationId],
  );
  const excludedOrganizationIds = useMemo(() => [organization.id], [organization.id]);
  const excludedUserIds = useMemo(
    () => [...organization.adminIds, ...organization.coachIds, ...organization.clientIds],
    [organization.adminIds, organization.clientIds, organization.coachIds],
  );
  const isAdmin = useIsAdmin();

  return (
    <Box my={1}>
      {isAdmin && !isFirstCreate && (
        <CopyClipboardWrapper text={organization.id}>
          <Text py={1}>Organization ID: {organization.id}</Text>
        </CopyClipboardWrapper>
      )}
      {DEV_FEATURE_FLAGS().isDebugViewEnabled && <Text>{JSON.stringify(organization)}</Text>}

      <TextInput
        error={hasNameChanged && isErrorName}
        errorLabel={CONTENT_CODES().ORGANIZATION.NAME_ERROR_LABEL}
        label={CONTENT_CODES().ORGANIZATION.NAME_LABEL}
        placeholder={CONTENT_CODES().ORGANIZATION.NAME_PLACEHOLDER}
        value={organization.name}
        onChangeText={text => {
          setHasNameChanged(true);
          onNameChange(text);
        }}
      />

      <Box my={1} />

      <Text variant='labelMedium'>{CONTENT_CODES().ORGANIZATION.TYPE_LABEL}</Text>
      {allOrganizationTypes.map(type => (
        <RadioButton
          key={type}
          isFilled={organization.type === type}
          label={
            <Box alignItems='center' flexDirection='row' justifyContent='center'>
              <Box pl={1} />
              <Text variant='labelMedium'>{getOrganizationTypeLabel(type)} </Text>
              <Box pl={2} />
              <Icon name={getOrganizationTypeIcon(type)} size={24} />
            </Box>
          }
          value={type}
          onPress={() => onTypeChange(type)}
        />
      ))}

      <Box pb={2} />

      <Accordion title={CONTENT_CODES().ORGANIZATION.SELECT_PARENT_ORGANIZATIONS_LABEL}>
        <SearchAndAddOrganizations
          organizationIdExceptions={excludedOrganizationIds}
          organizationIds={parentOrganizationIdArray}
          searchQueryKey='parentOrganizationSearch'
          selectedOrgsLabel={CONTENT_CODES().ORGANIZATION.SEARCH_SELECTED_PARENT_ORGANIZATION}
          onAdd={onChangeParentOrganization}
          onRemove={() => onChangeParentOrganization(undefined)}
        />
      </Accordion>

      <Box pb={2} />

      <Box pt={2} />
      <Accordion title={`${CONTENT_CODES().ORGANIZATION.SEARCH_ADMIN_USERS_LABEL}*`}>
        <SearchAndAddUsers
          isRequired
          searchQueryKey='adminUsersSearch'
          selectedUsersLabel={CONTENT_CODES().ORGANIZATION.SEARCH_ADMIN_SELECTED_LABEL}
          userIdExceptions={excludedUserIds}
          userIds={organization.adminIds}
          onAdd={onAddAdmin}
          onRemove={onRemoveAdmin}
        />
      </Accordion>
      <Box pt={2} />

      <Box pt={2} />
      <Accordion title={CONTENT_CODES().ORGANIZATION.SEARCH_COACH_USERS_LABEL}>
        <SearchAndAddUsers
          searchQueryKey='coachUsersSearch'
          selectedUsersLabel={CONTENT_CODES().ORGANIZATION.SEARCH_COACH_SELECTED_LABEL}
          userIdExceptions={excludedUserIds}
          userIds={organization.coachIds}
          onAdd={onAddCoach}
          onRemove={onRemoveCoach}
        />
      </Accordion>
      <Box pt={2} />

      <Box pt={2} />
      <Accordion title={CONTENT_CODES().ORGANIZATION.SEARCH_CLIENT_USERS_LABEL}>
        <SearchAndAddUsers
          searchQueryKey='clientUsersSearch'
          selectedUsersLabel={CONTENT_CODES().ORGANIZATION.SEARCH_CLIENT_SELECTED_LABEL}
          userIdExceptions={excludedUserIds}
          userIds={organization.clientIds}
          onAdd={onAddClient}
          onRemove={onRemoveClient}
        />
      </Accordion>
      <Box pt={2} />

      <Switch
        label='Is hide challenge tab?'
        value={organization.isHideChallenges ?? false}
        onChange={onToggleIsHideChallenges}
      />

      <Box pt={2} />

      {!isFirstCreate && !isChanged && (
        <Text py={2}>You must change the organization to save it</Text>
      )}
      {submitText && isChanged && (
        <Box mx='auto'>
          <ButtonContained
            disabled={isPendingSubmit || isError}
            icon='check'
            loading={isPendingSubmit}
            onPress={onSubmit}
          >
            {submitText}
          </ButtonContained>
        </Box>
      )}

      <Box mb={4} />
    </Box>
  );
};
