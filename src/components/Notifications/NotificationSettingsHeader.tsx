import {Box, Icon, Text} from '@base-components';
import type {StrictlyNoArgs} from '@types';

type NotificationSettingsHeaderProps = {
  onPressFrequency?: StrictlyNoArgs;
  title: string;
};

export const NotificationSettingsHeader: React.FC<NotificationSettingsHeaderProps> = ({
  onPressFrequency,
  title,
}) => (
  <Box flexDirection='row' justifyContent='space-between' minWidth='100%' pb={2}>
    <Text variant='titleMedium'>{title}</Text>
    {onPressFrequency && (
      <Icon name='calendar-clock-outline' size={24} onPress={() => onPressFrequency()} />
    )}
  </Box>
);
