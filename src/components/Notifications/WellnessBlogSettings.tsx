import {
  Box,
  ButtonOutlined,
  DropDownSelfContained,
  generateDropdownOptions,
  Icon,
  SwitchWithLabel,
  Text,
  TouchableHighlight,
} from '@base-components';
import {CONTENT_CODES, DOMAIN_CONSTANTS} from '@constants';
import {
  useGetPushNotificationTypeIsEnabled,
  useNotificationIntervalInDays,
  useNotificationTimeOfDay,
  useSetNotificationIntervalInDays,
  useSetNotificationTimeOfDay,
  useTogglePushNotificationDisabled,
  useTogglePushNotificationType,
  useToggleWellnessBlogCategory,
  useWellnessBlogCategoryEnabled,
  useWellnessBlogSettingsState,
} from '@contexts';
import {ALL_WELLNESS_BLOG_CATEGORIES, PushNotificationTypes} from '@types';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {ManagedModal} from '../Shared';
import {NotificationSettingsHeader} from './NotificationSettingsHeader';

const ALL_NOTIFICATION_INTERVAL_OPTIONS = generateDropdownOptions({
  start: DOMAIN_CONSTANTS().NOTIFICATIONS.MIN_INTERVAL_IN_DAYS,
  end: DOMAIN_CONSTANTS().NOTIFICATIONS.MAX_INTERVAL_IN_DAYS,
  interval: DOMAIN_CONSTANTS().NOTIFICATIONS.INTERVAL_STEP_IN_DAYS,
  defaultLabel: DOMAIN_CONSTANTS().NOTIFICATIONS.DEFAULT_WELLNESS_BLOG_INTERVAL_IN_DAYS,
  transformValue: v => v.toString(),
});

type WellnessBlogSettingsProps = Record<string, unknown>;
export const WellnessBlogSettings: React.FC<WellnessBlogSettingsProps> = () => {
  const {isWellnessBlogSettingsOpen, toggleValue} = useWellnessBlogSettingsState();
  const getType = useGetPushNotificationTypeIsEnabled();
  const isAllNotificationsEnabled = getType(PushNotificationTypes.WELLNESS_BLOG_DAILY);
  const toggle = useTogglePushNotificationType();
  const setNotificationOffsetTime = useSetNotificationTimeOfDay(
    PushNotificationTypes.WELLNESS_BLOG_DAILY,
  );
  const offsetDate = useNotificationTimeOfDay(PushNotificationTypes.WELLNESS_BLOG_DAILY);
  const intervalInDays = useNotificationIntervalInDays(PushNotificationTypes.WELLNESS_BLOG_DAILY);
  const setIntervalInDays = useSetNotificationIntervalInDays(
    PushNotificationTypes.WELLNESS_BLOG_DAILY,
  );
  const {toggle: toggleDisabled} = useTogglePushNotificationDisabled(
    PushNotificationTypes.WELLNESS_BLOG_DAILY,
  );
  const getBlogType = useWellnessBlogCategoryEnabled();
  const toggleBlogType = useToggleWellnessBlogCategory();

  return (
    <>
      <ManagedModal
        isNewBackgroundColor
        isOpen={isWellnessBlogSettingsOpen}
        onDismiss={toggleValue}
      >
        <NotificationSettingsHeader
          title={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.HEADER}
        />

        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.WELLNESS_BLOG_LABEL}
          value={getType(PushNotificationTypes.WELLNESS_BLOG_DAILY)}
          onValueChange={() => toggle(PushNotificationTypes.WELLNESS_BLOG_DAILY)}
        />

        <DateAndTimeComponent
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.TIME_LABEL}
          type='time'
          value={offsetDate}
          onChange={setNotificationOffsetTime}
        />

        <Box pt={2} />

        <DropDownSelfContained
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.FREQUENCY_LABEL}
          options={ALL_NOTIFICATION_INTERVAL_OPTIONS}
          value={intervalInDays}
          onChange={setIntervalInDays}
        />

        <Text pt={2} variant='labelMedium'>
          {CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.CATEGORY_HEADER}
        </Text>

        {ALL_WELLNESS_BLOG_CATEGORIES.map(category => (
          <SwitchWithLabel
            key={category}
            label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.CATEGORY_LABEL(
              category,
            )}
            value={getBlogType(category)}
            onValueChange={() => toggleBlogType(category)}
          />
        ))}
        <SwitchWithLabel
          label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.TOGGLE_ALL_LABEL}
          value={getBlogType(ALL_WELLNESS_BLOG_CATEGORIES)}
          onValueChange={() => toggleBlogType(ALL_WELLNESS_BLOG_CATEGORIES)}
        />

        <Box flexDirection='row' justifyContent='center' pt={2}>
          <ButtonOutlined onPress={() => toggleValue()}>
            {CONTENT_CODES().NOTIFICATIONS.SETTINGS.CLOSE_MODAL}
          </ButtonOutlined>
        </Box>
      </ManagedModal>

      <SwitchWithLabel
        label={CONTENT_CODES().NOTIFICATIONS.SETTINGS.WELLNESS_EDUCATION.BLOG.LABEL}
        right={
          isAllNotificationsEnabled && (
            <TouchableHighlight onPress={toggleValue}>
              <Box flexDirection='row' pr={1}>
                <Icon name='cog-outline' size={24} />
              </Box>
            </TouchableHighlight>
          )
        }
        value={isAllNotificationsEnabled}
        onValueChange={toggleDisabled}
      />
    </>
  );
};
