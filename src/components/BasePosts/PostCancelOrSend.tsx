import {Box, ButtonContained, ButtonOutlined} from '@base-components';
import type {IconSource} from '@types';

type PostCancelOrSendProps = {
  isHideCancel?: boolean;
  isImagePending: boolean;
  isPendingSubmit: boolean;
  isValid: boolean;
  onReset: () => void;
  onSubmit: () => void;
  submitIcon?: IconSource;
  submitLabel: string;
};

export const PostCancelOrSend: React.FC<PostCancelOrSendProps> = ({
  isHideCancel,
  isImagePending,
  isPendingSubmit,
  isValid,
  onReset,
  onSubmit,
  submitIcon = 'message-arrow-right-outline',
  submitLabel,
}) => (
  <Box flexDirection='row' justifyContent='space-between' px={1} py={1} width='100%'>
    {!isHideCancel && (
      <ButtonOutlined icon='close' onPress={onReset}>
        Cancel
      </ButtonOutlined>
    )}
    {isHideCancel && <Box />}
    <ButtonContained
      disabled={isImagePending || isPendingSubmit || !isValid}
      icon={submitIcon}
      loading={isPendingSubmit}
      onPress={onSubmit}
    >
      {submitLabel}
    </ButtonContained>
  </Box>
);
