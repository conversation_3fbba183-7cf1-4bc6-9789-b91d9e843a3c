import {Box, DropDownSelfContained, Switch, Text, TextLink} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {onLinkToStepLengthCalculator} from '@navigation';

const HEIGHT_MAP_LABELS = {
  'No height defined': '',
  /* eslint-disable @typescript-eslint/naming-convention -- for labels */
  '4"8': '56',
  '4"9': '57',
  '4"10': '58',
  '4"11': '59',
  '5"0': '60',
  '5"1': '61',
  '5"2': '62',
  '5"3': '63',
  '5"4': '64',
  '5"5': '65',
  '5"6': '66',
  '5"7': '67',
  '5"8': '68',
  '5"9': '69',
  '5"10': '70',
  '5"11': '71',
  '6"0': '72',
  '6"1': '73',
  '6"2': '74',
  '6"3': '75',
  '6"4': '76',
} as const;

const ALL_HEIGHT_OPTIONS = Object.entries(HEIGHT_MAP_LABELS).map(([key, value]) => ({
  value,
  label: key,
}));

const STEP_LENGTH_MAP_LABELS = {
  '22"': '22',
  '23"': '23',
  '24"': '24',
  '25"': '25',
  '26"': '26',
  '27"': '27',
  '28"': '28',
  '29" (default)': '29 (default)',
  '30"': '30',
  '31"': '31',
  '32"': '32',
  '33"': '33',
  '34"': '34',
  '35"': '35',
  '36"': '36',
  '37"': '37',
  '38"': '38',
  '39"': '39',
  '40"': '40',
  /* eslint-enable @typescript-eslint/naming-convention */
} as const;

const ALL_STEPS_OPTIONS = Object.entries(STEP_LENGTH_MAP_LABELS).map(([key, value]) => ({
  value,
  label: key,
}));

type EditHeightAndStepLengthProps = {
  data: {
    isStepLengthRecalculate: boolean;
    onHeightChange: (height: string | undefined) => void;
    onStepLengthChange: (stepLength: string | undefined) => void;
    onStepLengthRecalculate: () => void;
    selectedHeight?: number | undefined;
    stepLengthStr?: string | undefined;
  };
};

export const EditHeightAndStepLength: React.FC<EditHeightAndStepLengthProps> = ({
  data: {
    isStepLengthRecalculate,
    onHeightChange,
    onStepLengthChange,
    onStepLengthRecalculate,
    selectedHeight,
    stepLengthStr,
  },
}) => (
  <>
    <Switch
      label='Override step length?'
      value={isStepLengthRecalculate}
      valueLabelFalse='No (default)'
      valueLabelTrue='Yes'
      onValueChange={onStepLengthRecalculate}
    />

    <Box pb={1} />

    {isStepLengthRecalculate && (
      <>
        <DropDownSelfContained
          label='Height (feet/inches)'
          options={ALL_HEIGHT_OPTIONS}
          value={String(selectedHeight ?? '')}
          onChange={onHeightChange}
        />
        <Text pt={1} px={1} variant='bodySmall'>
          Height can be used to estimate your step length if you do not know it exactly.
        </Text>
        <Box pb={2} />
      </>
    )}
    <DropDownSelfContained
      isDisabled={!isStepLengthRecalculate}
      label={CONTENT_CODES().EDIT_USER.STEP_LENGTH_LABEL}
      options={ALL_STEPS_OPTIONS}
      value={stepLengthStr ?? '29 (default)'}
      onChange={onStepLengthChange}
    />
    {isStepLengthRecalculate && (
      <>
        <Text pt={1} px={1} variant='bodySmall'>
          You can override your step length directly if you know it.
        </Text>
        <Text pt={1} px={1} variant='bodySmall'>
          Check out this{' '}
          <TextLink onPress={onLinkToStepLengthCalculator}>step length calculator</TextLink> if
          you would like to calculate it yourself.
        </Text>
      </>
    )}
  </>
);
