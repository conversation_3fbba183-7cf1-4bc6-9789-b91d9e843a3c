import {Box, DropDownSelfContained, generateDropdownOptions, Icon, Text} from '@base-components';
import {DOMAIN_CONSTANTS} from '@constants';
import {milesToMetersFormatted} from '@utils';

const ALL_GOAL_MILEAGE_OPTIONS = generateDropdownOptions({
  start: DOMAIN_CONSTANTS().GOALS.MIN_GOAL_MILEAGE_IN_MILES,
  end: DOMAIN_CONSTANTS().GOALS.MAX_GOAL_MILEAGE_IN_MILES,
  interval: DOMAIN_CONSTANTS().GOALS.GOAL_MILEAGE_INTERVAL,
  defaultLabel: DOMAIN_CONSTANTS().GOALS.DEFAULT_GOAL_MILEAGE_IN_MILES,
  formatLabel: key => key.toFixed(1),
  transformValue: milesToMetersFormatted,
});

const ALL_GOAL_STEP_COUNT_OPTIONS = generateDropdownOptions({
  start: DOMAIN_CONSTANTS().GOALS.MIN_GOAL_STEP_COUNT,
  end: DOMAIN_CONSTANTS().GOALS.MAX_GOAL_STEP_COUNT,
  interval: DOMAIN_CONSTANTS().GOALS.GOAL_STEP_COUNT_INTERVAL,
  defaultLabel: DOMAIN_CONSTANTS().GOALS.DEFAULT_GOAL_STEP_COUNT,
  formatLabel: key => key.toLocaleString('en-US'),
  transformValue: v => v.toString(),
});

type EditGoalsProps = {
  data: {
    dailyGoalMileageInMeters: number;
    dailyGoalStepCount: number;
    hasGoalsChanged: boolean;
    onDailyGoalMileageInMetersChange: (value: string) => void;
    onDailyGoalStepCountChange: (value: string) => void;
    onResetDailyGoals: () => void;
  };
  isDisableHelpMessages?: boolean | undefined;
};

export const EditGoals: React.FC<EditGoalsProps> = ({
  data: {
    dailyGoalMileageInMeters,
    dailyGoalStepCount,
    hasGoalsChanged,
    onDailyGoalMileageInMetersChange,
    onDailyGoalStepCountChange,
    onResetDailyGoals,
  },
  isDisableHelpMessages = false,
}) => (
  <>
    <DropDownSelfContained
      label='Daily Mileage Goal (miles)'
      options={ALL_GOAL_MILEAGE_OPTIONS}
      value={String(dailyGoalMileageInMeters)}
      onChange={onDailyGoalMileageInMetersChange}
    />
    <Box pb={2} />
    <DropDownSelfContained
      label='Daily Step Count Goal (steps)'
      options={ALL_GOAL_STEP_COUNT_OPTIONS}
      value={String(dailyGoalStepCount)}
      onChange={onDailyGoalStepCountChange}
    />
    {hasGoalsChanged && (
      <Box flexDirection='row' justifyContent='flex-end'>
        <Text onPress={onResetDailyGoals}>
          <Icon name='undo' size={20} /> Reset
        </Text>
      </Box>
    )}
    {!isDisableHelpMessages && (
      <>
        <Text pt={1} px={1} variant='bodySmall'>
          The daily mileage and step goals are linked together based on your step length. Changing
          one goal will automatically update the other.
        </Text>
        <Text pt={1} px={1} variant='bodySmall'>
          The calculation of mileage and step count rounds to the nearest available goal number.
        </Text>
      </>
    )}
  </>
);
