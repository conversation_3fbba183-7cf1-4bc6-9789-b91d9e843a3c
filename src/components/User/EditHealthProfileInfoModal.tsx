import {useEffect} from 'react';
import {Box, ButtonContained, Switch, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useEditHealthProfileInfoState} from '@contexts';
import type {AppUser} from '@types';
import {ManagedModal} from '../Shared';
import {EditHeightAndStepLength} from './EditHeightAndStepLengthProps';

type EdithealthProfileInfoProps = {
  initialState: AppUser;
  isOpen: boolean;
  onDismiss: () => void;
};

export const EditHealthProfileInfoModal: React.FC<EdithealthProfileInfoProps> = ({
  initialState,
  isOpen,
  onDismiss,
}) => {
  const {
    appUser,
    isChanged,
    isPending,
    isStepLengthRecalculate,
    isSuccess,
    onHeightChange,
    onIsMileageGpsSourcedChange,
    onStepLengthChange,
    onStepLengthRecalculate,
    onSubmit,
    stepLengthStr,
  } = useEditHealthProfileInfoState(initialState);

  useEffect(() => {
    if (!isSuccess || isPending) return;
    onDismiss();
  }, [isPending, isSuccess, onDismiss]);

  return (
    <ManagedModal isNewBackgroundColor isOpen={isOpen} onDismiss={onDismiss}>
      <Text py={1} variant='labelLarge'>
        {CONTENT_CODES().EDIT_USER.HEALTH_PROFILE_LABEL}
      </Text>

      <Box my={1}>
        <EditHeightAndStepLength
          data={{
            selectedHeight: appUser.heightInInches,
            onHeightChange,
            onStepLengthChange,
            stepLengthStr,
            isStepLengthRecalculate,
            onStepLengthRecalculate,
          }}
        />
      </Box>

      <Box mt={1} />

      <Switch
        label={CONTENT_CODES().EDIT_USER.MILEAGE_SOURCE_LABEL}
        value={!(appUser.isMileageGpsSourced ?? false)}
        valueLabelFalse='No'
        valueLabelTrue='Yes (default)'
        onValueChange={onIsMileageGpsSourcedChange}
      />

      <Box mx='auto' py={1}>
        <ButtonContained
          disabled={isPending || !isChanged}
          icon='check'
          loading={isPending}
          onPress={onSubmit}
        >
          {CONTENT_CODES().EDIT_USER.SUBMIT_UPDATE_PROFILE_LABEL}
        </ButtonContained>
      </Box>
    </ManagedModal>
  );
};
