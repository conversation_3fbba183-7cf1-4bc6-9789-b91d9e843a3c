import {useCallback, useEffect, useState} from 'react';
import {Images} from '@assets';
import {Box, ButtonContained, Confetti, Icon, Text, TouchableHighlight} from '@base-components';
import {CONTENT_CODES, DOMAIN_CONSTANTS} from '@constants';
import {
  useAppUserSafe,
  useEditUserProfileState,
  useIsEditGoalChallengeOpenState,
  useIsEditGoalHomeOpenState,
} from '@contexts';
import type {ChildrenProps} from '@types';
import {getLatestGoalsOrDefault, useAppTheme} from '@utils';
import {ManagedModal} from '../Shared';
import {EditGoals} from './EditGoals';

type ModalContentProps = {
  isForChallenge: boolean;
  onDismiss: () => void;
  setIsOnConfirmation: (value: boolean) => void;
};

// eslint-disable-next-line max-lines-per-function -- long to handle the 3 modal screen
const ModalContent: React.FC<ModalContentProps> = ({
  isForChallenge,
  onDismiss,
  setIsOnConfirmation,
}) => {
  const appUserAuth = useAppUserSafe();
  const [screenState, setScreenState] = useState<'explanation' | 'main' | 'confirmation' | 'close'>(
    'main',
  );
  const onGoToExplanation = useCallback(() => setScreenState('explanation'), []);
  const onNext = useCallback(
    () =>
      setScreenState(p => {
        if (p === 'main') return 'confirmation';
        if (p === 'explanation') return 'main';
        if (p === 'confirmation') return 'close';

        return p;
      }),
    [],
  );
  useEffect(() => {
    setIsOnConfirmation(screenState === 'confirmation');
    if (screenState === 'close') onDismiss();
  }, [onDismiss, screenState, setIsOnConfirmation]);
  const {
    appUser,
    isGoalsChanged,
    isPending,
    onDailyGoalMileageInMetersChange,
    onDailyGoalStepCountChange,
    onResetDailyGoals,
    onSubmit,
  } = useEditUserProfileState(appUserAuth, onNext);
  const {dailyGoalMileageInMeters, dailyGoalStepCount} = getLatestGoalsOrDefault(appUser);
  const theme = useAppTheme();

  return (
    <>
      {screenState === 'explanation' && (
        <>
          <Text pb={2} pt={2}>
            {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_1}
          </Text>
          <Text pb={2}>{CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_2}</Text>
          <Text style={{fontFamily: DOMAIN_CONSTANTS().FONT.BOLD_FAMILY}}>
            {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_3}
          </Text>
          <Text style={{fontFamily: DOMAIN_CONSTANTS().FONT.BOLD_FAMILY}}>
            {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_4}
          </Text>
          <Text pb={2} style={{fontFamily: DOMAIN_CONSTANTS().FONT.BOLD_FAMILY}}>
            {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_5}
          </Text>

          <Text pb={2}>{CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_6}</Text>

          <Box flexDirection='row' justifyContent='center'>
            <ButtonContained icon='arrow-right-circle-outline' onPress={onNext}>
              {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_CONTINUE}
            </ButtonContained>
          </Box>
        </>
      )}
      {screenState === 'main' && (
        <>
          <Box pt={2} />
          {isForChallenge && <Text pb={1}>{CONTENT_CODES().EDIT_USER.GOALS_MODAL.SUBTITLE}</Text>}
          <Box my={1}>
            <EditGoals
              isDisableHelpMessages
              data={{
                dailyGoalMileageInMeters,
                dailyGoalStepCount,
                onDailyGoalMileageInMetersChange,
                onDailyGoalStepCountChange,
                onResetDailyGoals,
                hasGoalsChanged: isGoalsChanged,
              }}
            />
          </Box>

          <TouchableHighlight onPress={onGoToExplanation}>
            <Text
              my={1}
              style={{color: theme.colors.linkColor, lineHeight: 24, textAlign: 'center'}}
              variant='labelLarge'
            >
              {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EXPLANATION_BUTTON}
            </Text>
          </TouchableHighlight>

          {isForChallenge && (
            <Text pt={2} variant='bodySmall'>
              {CONTENT_CODES().EDIT_USER.GOALS_MODAL.EDIT_LATER_MSG}
            </Text>
          )}

          <Box pt={3} />

          <Box flexDirection='row' justifyContent='center'>
            <ButtonContained
              disabled={isPending}
              icon='check'
              loading={isPending}
              onPress={onSubmit}
            >
              {CONTENT_CODES().EDIT_USER.GOALS_MODAL.SAVE_BUTTON}
            </ButtonContained>
          </Box>
        </>
      )}
      {(screenState === 'confirmation' || screenState === 'close') && (
        <>
          <Text
            py={2}
            style={{
              fontFamily: DOMAIN_CONSTANTS().FONT.BOLD_FAMILY,
              fontSize: 18,
              textAlign: 'center',
            }}
          >
            {isForChallenge
              ? CONTENT_CODES().EDIT_USER.GOALS_MODAL.CONFIRMATION_CHALLENGE
              : CONTENT_CODES().EDIT_USER.GOALS_MODAL.CONFIRMATION_STANDARD}
          </Text>
          <Text pb={1}>{CONTENT_CODES().EDIT_USER.GOALS_MODAL.CONFIRMATION_MOVEMENT_STREAK}</Text>
          <Images.movementStreakExample contentFit='contain' style={{height: 180}} />

          <Box pb={4} />

          <Box flexDirection='row' justifyContent='center'>
            <ButtonContained icon='walk' onPress={onNext}>
              {CONTENT_CODES().EDIT_USER.GOALS_MODAL.CONFIRMATION_CONTINUE_BUTTON}
            </ButtonContained>
          </Box>
        </>
      )}
    </>
  );
};

type EditGoalModalProps = Partial<ChildrenProps> & {
  isForChallenge?: boolean | undefined;
};

type EditGoalModalInternalProps = EditGoalModalProps & {
  isOpen: boolean;
  toggleIsOpen: () => void;
};

const EditGoalModalInternal: React.FC<EditGoalModalInternalProps> = ({
  children,
  isForChallenge = false,
  isOpen,
  toggleIsOpen,
}) => {
  const [isOnConfirmation, setIsOnConfirmation] = useState(false);
  const content = (
    <ModalContent
      isForChallenge={isForChallenge}
      setIsOnConfirmation={setIsOnConfirmation}
      onDismiss={toggleIsOpen}
    />
  );

  return (
    <>
      {children && <TouchableHighlight onPress={toggleIsOpen}>{children}</TouchableHighlight>}
      <ManagedModal
        isFullWidth
        isNewBackgroundColor
        isOpen={isOpen}
        outsideSurfaceElement={isOnConfirmation && <Confetti />}
        onDismiss={toggleIsOpen}
      >
        <Text textAlign='center' variant='headlineMedium'>
          <Icon name='bullseye-arrow' size={24} /> {CONTENT_CODES().EDIT_USER.GOALS_MODAL.TITLE}
        </Text>
        {content}
      </ManagedModal>
    </>
  );
};

export const EditGoalModalHome: React.FC<ChildrenProps> = ({children}) => {
  const {isEditGoalHomeOpen, toggleValue} = useIsEditGoalHomeOpenState();

  return (
    <EditGoalModalInternal isOpen={isEditGoalHomeOpen} toggleIsOpen={toggleValue}>
      {children}
    </EditGoalModalInternal>
  );
};

export const EditGoalModalChallenge: React.FC = () => {
  const {isEditGoalChallengeOpen, toggleValue} = useIsEditGoalChallengeOpenState();

  return (
    <EditGoalModalInternal
      isForChallenge
      isOpen={isEditGoalChallengeOpen}
      toggleIsOpen={toggleValue}
    />
  );
};
