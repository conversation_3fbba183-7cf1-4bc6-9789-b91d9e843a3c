import {useState} from 'react';
import {Box, ButtonContained, Icon, List, RadioButton, Text, TextInput} from '@base-components';
import {CONTENT_CODES, isAndroid, isIos} from '@constants';
import {
  useEmailTrackingDeviceTypeConnection,
  useSetTrackingApp,
  useSetTrackingDeviceType,
  useTrackingApp,
  useTrackingDevicePopUpState,
  useTrackingDeviceType,
} from '@contexts';
import {useOnMount} from '@hooks';
import {
  allTrackingAppsAndroid,
  allTrackingDeviceTypesAndroid,
  allTrackingDeviceTypesIos,
  getTrackingAppDisplayName,
  getTrackingDeviceTypeDisplayName,
  isPrimaryTrackingDeviceType,
  TrackingApps,
  TrackingDeviceTypes,
} from '@types';
import {memoComponent} from '@utils';
import {ManagedModal} from '../Shared';
import {FullHealthSyncReload} from './FullHealthSyncReload';
import {TrackingDeviceExplanations} from './TrackingDeviceExplanations';

const StartContent: React.FC = () => (
  <>
    <Text pb={2}>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.START_DESCRIPTION_1}</Text>
    <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.START_DESCRIPTION_2}</Text>
  </>
);

const allDeviceTypes = isIos ? allTrackingDeviceTypesIos : allTrackingDeviceTypesAndroid;

const TrackingDeviceQuestion: React.FC = () => {
  const selectedTrackingDeviceType = useTrackingDeviceType();
  const trackingApp = useTrackingApp();
  const setTrackingDevice = useSetTrackingDeviceType();

  return (
    <>
      <Text variant='labelMedium'>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ASH_WHICH_TRACKING_DEVICE}</Text>
      <List.Section>
        {allDeviceTypes.map(type => (
          <RadioButton
            key={`has-tracking-option-${type}`}
            isDense
            isFilled={type === selectedTrackingDeviceType}
            label={
              <Box alignItems='center' flexDirection='row'>
                <Text pl={1} variant='labelMedium'>{getTrackingDeviceTypeDisplayName(type)}</Text>
              </Box>
            }
            value={type}
            onPress={() => {
              void setTrackingDevice({trackingDeviceType: type, trackingApp});
            }}
          />
        ))}
      </List.Section>
    </>
  );
};

const AdditionalExplanation: React.FC = () => {
  const trackingDeviceType = useTrackingDeviceType();
  const emailWearableConnectionMutation = useEmailTrackingDeviceTypeConnection();

  useOnMount(() => {
    // Send tracking device email if supported
    if (isPrimaryTrackingDeviceType(trackingDeviceType)) {
      emailWearableConnectionMutation.mutate(trackingDeviceType);
    }
  });

  return <TrackingDeviceExplanations isContentOnly isShowEmail />;
};

const TrackingAppQuestion: React.FC<{
  isPending: boolean;
  onContinue: () => void;
  setIsPending: (value: boolean) => void;
}> = ({isPending, onContinue, setIsPending}) => {
  const trackingDeviceType = useTrackingDeviceType();
  const trackingApp = useTrackingApp();
  const [localTrackingApp, setLocalTrackingApp] = useState(trackingApp ?? '');
  const [otherTrackingApp, setOtherTrackingApp] = useState('');
  const setTrackingApp = useSetTrackingApp();

  // Continue if one of the tracking device options to prompt for
  const isStay =
    isAndroid &&
    trackingDeviceType &&
    (trackingDeviceType === TrackingDeviceTypes.NoDevice ||
      trackingDeviceType === TrackingDeviceTypes.Other);

  useOnMount(() => {
    if (!isStay) {
      onContinue();
    }
  });

  if (!isStay) return null;

  return (
    <>
      <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ASK_WHICH_TRACKING_APP}</Text>
      <List.Section>
        {allTrackingAppsAndroid.map(type => (
          <RadioButton
            key={`has-tracking-app-option-${type}`}
            isDense
            isFilled={type === localTrackingApp}
            label={
              <Box alignItems='center' flexDirection='row'>
                <Text pl={1}>{getTrackingAppDisplayName(type)}</Text>
              </Box>
            }
            value={type}
            onPress={() => setLocalTrackingApp(type)}
          />
        ))}
      </List.Section>
      {localTrackingApp === TrackingApps.Other && (
        <Box px={2}>
          <TextInput
            label={CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.TRACKING_APP_LABEL}
            placeholder={
              CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.TRACKING_APP_PLACEHOLDER
            }
            value={otherTrackingApp}
            onChangeText={setOtherTrackingApp}
          />
        </Box>
      )}
      <Box flexDirection='row' justifyContent='center' pt={2}>
        <ButtonContained
          disabled={isPending}
          loading={isPending}
          onPress={async () => {
            setIsPending(true);
            await setTrackingApp(otherTrackingApp || localTrackingApp);
            setIsPending(false);
            onContinue();
          }}
        >
          {CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CONTINUE}
        </ButtonContained>
      </Box>
    </>
  );
};

const AllSetContent: React.FC<{isPending: boolean; setIsPending: (value: boolean) => void}> = ({
  isPending,
  setIsPending,
}) => (
  <>
    <Text pb={2} textAlign='center' variant='bodyLarge'>
      {isPending
        ? CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.RELOADING_HEALTH_DATA
        : CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ALL_SET}
    </Text>
    {!isPending && (
      <Text>{CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.ALL_SET_EXPLANATION}</Text>
    )}
    <FullHealthSyncReload enableOnMount hideUi onIsPendingChange={setIsPending} />
  </>
);

type TrackingDeviceModalProps = Record<string, unknown>;

export const TrackingDeviceModal: React.FC<TrackingDeviceModalProps> = memoComponent(() => {
  const {isDismissable, onContinue, onDismiss, trackingDevicePopUpState} =
    useTrackingDevicePopUpState();
  const [isPending, setIsPending] = useState(false);
  const isDisabled = !isDismissable && trackingDevicePopUpState === 'closed';

  return (
    <ManagedModal
      isFullWidth
      isNewBackgroundColor
      isDismissable={isDismissable}
      isOpen={trackingDevicePopUpState !== 'closed'}
      onDismiss={onDismiss}
    >
      <Box alignItems='center' pb={1}>
        <Icon name='watch' size={48} />
      </Box>

      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.HEADER}
      </Text>

      <Box py={1} />

      {trackingDevicePopUpState === 'start' && <StartContent />}
      {trackingDevicePopUpState === 'deviceTrackingType' && <TrackingDeviceQuestion />}
      {trackingDevicePopUpState === 'additionalExplanation' && <AdditionalExplanation />}
      {trackingDevicePopUpState === 'trackingApp' && (
        <TrackingAppQuestion
          isPending={isPending}
          setIsPending={setIsPending}
          onContinue={onContinue}
        />
      )}
      {trackingDevicePopUpState === 'allSet' && (
        <AllSetContent isPending={isPending} setIsPending={setIsPending} />
      )}

      {trackingDevicePopUpState !== 'trackingApp' && (
        <Box flexDirection='row' justifyContent='center' pt={2}>
          <ButtonContained
            disabled={isPending || isDisabled}
            loading={isPending}
            onPress={onContinue}
          >
            {trackingDevicePopUpState === 'allSet'
              ? CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CLOSE
              : CONTENT_CODES().EXPLANATIONS.TRACKING_DEVICE_POP_UP.CONTINUE}
          </ButtonContained>
        </Box>
      )}
    </ManagedModal>
  );
});
