import {useState} from 'react';
import {Images} from '@assets';
import {Box, ButtonContained, ButtonOutlined, Icon, Text, TouchableHighlight} from '@base-components';
import {CONTENT_CODES, isAndroid, isIos} from '@constants';
import {
  requestAllHealthPermissions,
  requestStepHealthPermissions,
  requestWeightHealthPermissions,
  useHasAllHealthPermissions,
  useHasAllStepPermissions,
  useHasAllWeightPermissions,
  useInvalidateAllPermissions,
} from '@contexts';
import {useOnMountAsync} from '@hooks';
import {onLinkToHealthApp, useLinkTo} from '@navigation';
import {memoComponent} from '@utils';
import {ManagedModal} from '../Shared';

const CONTENT_MAP = {
  steps: {
    title: 'Steps',
    description: 'step',
  },
  weight: {
    title: 'Weight',
    description: 'weight',
  },
  all: {
    title: 'Data',
    description: undefined,
  },
} as const;

type HealthSyncModalProps = {
  hideButton?: boolean | undefined;
  isHealthSyncEnabled: boolean | undefined;
  isOpenInitialState?: boolean | undefined;
  onComplete?: (() => void) | undefined;
  permissionType: 'steps' | 'weight' | 'all';
  refetch: () => Promise<void>;
  requestPermissions: () => Promise<boolean>;
};

// eslint-disable-next-line max-lines-per-function -- TODO refactor
const HealthSyncModal: React.FC<HealthSyncModalProps> = ({
  hideButton = false,
  isHealthSyncEnabled,
  isOpenInitialState = false,
  onComplete,
  permissionType,
  refetch,
  requestPermissions,
}) => {
  const to = useLinkTo();
  const [isOpen, setIsOpen] = useState(isOpenInitialState);
  const [isWarningModalOpen, setIsWarningModalOpen] = useState(false);
  const isAnyModalOpen = isOpen || isWarningModalOpen;
  useOnMountAsync(refetch);
  const invalidate = useInvalidateAllPermissions();

  if (isHealthSyncEnabled === undefined || isHealthSyncEnabled) return null;

  return (
    <>
      {!hideButton && (
        <TouchableHighlight
          disabled={isAnyModalOpen}
          onLongPress={() => to.healthSyncSettings()}
          onPress={() => setIsOpen(true)}
        >
          <Box
            style={{
              backgroundColor: '#fff',
              alignItems: 'center',
              borderRadius: 10,
              padding: 8,
              marginBottom: 8,
              justifyContent: 'space-between',
              flexDirection: 'row',
            }}
          >
            <Box alignItems='center' flexDirection='row'>
              {isIos && <Images.appleHealthIcon style={{width: 50, height: 50}} />}
              {isAndroid && <Images.googleHealthConnectIcon style={{width: 50, height: 50}} />}
              <Box pl={1}>
                <Icon name='sync' size={20} />
              </Box>
              <Text variant='headlineSmall'>
                {' '}
                {CONTENT_CODES().PERMISSIONS.SYNC.TITLE(CONTENT_MAP[permissionType].title)}
              </Text>
            </Box>
            <Box py={1}>
              <Icon name='chevron-right' size={32} />
            </Box>
          </Box>
        </TouchableHighlight>
      )}

      <ManagedModal
        isNewBackgroundColor
        isOpen={isOpen}
        onDismiss={() => setIsOpen(false)}
      >
        <Text pb={2} textAlign='center' variant='headlineMedium'>
          <Icon name='sync' size={32} />
          {CONTENT_CODES().PERMISSIONS.SYNC.TITLE(CONTENT_MAP[permissionType].title)}
        </Text>
        <Text pb={2} variant='bodyLarge'>
          {CONTENT_CODES().PERMISSIONS.SYNC.DESCRIPTION_1(CONTENT_MAP[permissionType].description)}
        </Text>
        <Text pb={2} variant='bodyLarge'>
          {CONTENT_CODES().PERMISSIONS.SYNC.DESCRIPTION_2}
        </Text>
        <Text pb={2} style={{fontStyle: 'italic'}}>
          {CONTENT_CODES().PERMISSIONS.SYNC.DESCRIPTION_WARNING}
        </Text>
        <Box flexDirection='row' justifyContent='space-between' pt={2}>
          <ButtonOutlined onPress={() => setIsOpen(false)}>
            {CONTENT_CODES().PERMISSIONS.SYNC.CANCEL}
          </ButtonOutlined>
          <ButtonContained
            icon='arrow-right-circle-outline'
            onPress={async () => {
              const isPermissionsGranted = await requestPermissions();
              setIsOpen(false);
              if (isPermissionsGranted) {
                await invalidate();
                onComplete?.();
              } else {
                setIsWarningModalOpen(true);
              }
            }}
          >
            {CONTENT_CODES().PERMISSIONS.SYNC.CONTINUE}
          </ButtonContained>
        </Box>
      </ManagedModal>
      <ManagedModal
        isNewBackgroundColor
        isOpen={isWarningModalOpen}
        onDismiss={() => {
          setIsWarningModalOpen(false);
          onComplete?.();
        }}
      >
        <Text pb={2} textAlign='center' variant='headlineMedium'>
          <Icon name='alert-circle-outline' size={32} />
          {CONTENT_CODES().PERMISSIONS.SYNC.WARNING_HEADER}
        </Text>
        <Text pb={2}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DESCRIPTION_1}</Text>
        <Text pb={2}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DESCRIPTION_2}</Text>
        <Text pb={1}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DIRECTIONS_1}</Text>
        <Text pb={1}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DIRECTIONS_2}</Text>
        <Text pb={1}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DIRECTIONS_3}</Text>
        <Text pb={1}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DIRECTIONS_4}</Text>
        <Text pb={1}>{CONTENT_CODES().PERMISSIONS.SYNC.WARNING_DIRECTIONS_5}</Text>

        {isIos && <Images.appleHealthAllowPermissions contentFit='contain' style={{height: 300}} />}

        <Box flexDirection='row' justifyContent='space-between' pt={2}>
          <ButtonOutlined
            onPress={() => {
              setIsWarningModalOpen(false);
              onComplete?.();
            }}
          >
            {CONTENT_CODES().PERMISSIONS.SYNC.CANCEL}
          </ButtonOutlined>
          <ButtonContained icon='open-in-app' onPress={onLinkToHealthApp}>
            {CONTENT_CODES().PERMISSIONS.SYNC.OPEN_HEALTH_APP}
          </ButtonContained>
        </Box>
      </ManagedModal>
    </>
  );
};

export const HealthSyncWeightModal: React.FC = () => {
  const {data: hasPermissions, refetch} = useHasAllWeightPermissions();
  // Don't show or load if the user has all permissions
  if (hasPermissions) return null;

  return (
    <HealthSyncModal
      isHealthSyncEnabled={hasPermissions}
      permissionType='weight'
      refetch={refetch as unknown as () => Promise<void>}
      requestPermissions={requestWeightHealthPermissions}
    />
  );
};

export const HealthSyncStepModal: React.FC<{hasPaddingTop?: boolean}> = memoComponent(
  ({hasPaddingTop}) => {
    const {data: hasPermissions, refetch} = useHasAllStepPermissions();
    // Don't show or load if the user has all permissions
    if (hasPermissions) return null;

    return (
      <Box pt={hasPaddingTop ? 1 : 0}>
        <HealthSyncModal
          isHealthSyncEnabled={hasPermissions}
          permissionType='steps'
          refetch={refetch as unknown as () => Promise<void>}
          requestPermissions={requestStepHealthPermissions}
        />
      </Box>
    );
  },
);

export const HealthSyncAllModal: React.FC<Pick<HealthSyncModalProps, 'onComplete'>> = ({
  onComplete,
}) => {
  const {data: hasPermissions, refetch} = useHasAllHealthPermissions();
  // Don't show or load if the user already has all permissions
  if (hasPermissions) return null;

  return (
    <HealthSyncModal
      hideButton
      isOpenInitialState
      isHealthSyncEnabled={hasPermissions}
      permissionType='all'
      refetch={refetch as unknown as () => Promise<void>}
      requestPermissions={requestAllHealthPermissions}
      onComplete={onComplete}
    />
  );
};
