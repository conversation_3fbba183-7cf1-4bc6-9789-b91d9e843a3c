import {useCallback, useState} from 'react';
import {Box, ButtonContained, ButtonOutlined, TextInput} from '@base-components';
import {useSubmitManualEntry} from '@contexts';
import {
  type AppUserHealthData,
  dateToRawTimestamp,
  type HealthStats,
  type HealthStatsDates,
  type IsoDate,
} from '@types';
import {isDeepEqual, metersToMiles, milesToMeters, roundTo2Decimal, useAppTheme} from '@utils';
import {DateAndTimeComponent} from '../DateAndTimeComponent';

type EditManualEntryProps = {
  appUser: AppUserHealthData;
  initialEntry: HealthStatsDates & {isoDate: IsoDate};
  onSuccess?: (() => void) | undefined;
};

export const EditManualEntry: React.FC<EditManualEntryProps> = ({
  appUser,
  initialEntry,
  onSuccess,
}) => {
  const theme = useAppTheme();
  const initialEntryStrings = {
    startDate: initialEntry.startDate,
    endDate: initialEntry.endDate,
    stepsCount: initialEntry.stepsCount ? String(initialEntry.stepsCount) : '',
    isOverrideEntry: initialEntry.isOverrideEntry,
    distanceMileage: initialEntry.distanceMeters
      ? String(roundTo2Decimal(metersToMiles(initialEntry.distanceMeters)))
      : '',
  };
  const [state, setState] = useState(initialEntryStrings);
  const isValid = !!state.stepsCount || !!state.distanceMileage;
  const isValidDelete = isValid && state.isOverrideEntry;
  const hasChanged = !isDeepEqual(initialEntryStrings, state);
  const {isPending, mutateAsync} = useSubmitManualEntry(appUser);

  const onSubmit = useCallback(
    async (isDeleteOverlapping?: boolean) => {
      if (!isDeleteOverlapping && !hasChanged) return;

      const statWithProperTypes: HealthStats = {
        startDate: dateToRawTimestamp(state.startDate),
        endDate: dateToRawTimestamp(state.endDate),
        stepsCount: Number(state.stepsCount),
        distanceMeters: Number(milesToMeters(Number(state.distanceMileage))),
        isOverrideEntry: true,
      };

      await mutateAsync({entry: statWithProperTypes, isDeleteOverlapping});
      onSuccess?.();
    },
    [
      hasChanged,
      mutateAsync,
      onSuccess,
      state.distanceMileage,
      state.endDate,
      state.startDate,
      state.stepsCount,
    ],
  );
  const isLargeStepsValue = Number(state.stepsCount) >= 80_000;
  const isLargeMileageValue = Number(state.distanceMileage) >= 40;

  return (
    <Box>
      <DateAndTimeComponent isReadOnly label='Date' value={state.startDate} />

      <Box pb={1} />

      <TextInput
        autoFocus
        error={isLargeStepsValue}
        errorLabel='80,000+ steps is unusually high. Did you mean to enter this amount?'
        keyboardType='numeric'
        label='Steps'
        placeholder='0'
        value={state.stepsCount}
        onChangeText={stepsCount => setState(p => ({...p, stepsCount}))}
      />

      <Box pb={1} />

      <TextInput
        error={isLargeMileageValue}
        errorLabel='40+ miles is unusually high. Did you mean to enter this amount?'
        keyboardType='numeric'
        label='Mileage'
        placeholder='0.0'
        value={state.distanceMileage}
        onChangeText={distanceMileage => setState(p => ({...p, distanceMileage}))}
      />

      <Box pb={4} />

      <Box flexDirection='row' justifyContent='space-between' width='100%'>
        <ButtonOutlined
          disabled={isPending || !isValidDelete}
          icon='delete'
          textColor={theme.colors.delete}
          onPress={() => onSubmit(true)}
        >
          Delete
        </ButtonOutlined>
        <ButtonContained
          buttonColor={theme.colors.dodgerBlue}
          disabled={!hasChanged || isPending || !isValid}
          icon='content-save-outline'
          loading={isPending}
          onPress={() => onSubmit()}
        >
          Save
        </ButtonContained>
      </Box>

      <Box pb={4} />
    </Box>
  );
};
