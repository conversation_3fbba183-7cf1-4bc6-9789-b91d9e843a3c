import {openURL} from 'expo-linking';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {
  Box,
  ButtonContained,
  ButtonOutlined,
  IconButton,
  MultiSelect,
  PhotoListInput,
  StickyAboveNavigation,
  Switch,
  Text,
  TextInput,
  TextLink,
  TimePicker,
} from '@base-components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS, DOMAIN_CONSTANTS} from '@constants';
import {useCopyToClipboard, useIsCoach, useIsWorkoutTodayOrBefore, useWorkoutState} from '@contexts';
import {useCurrentTimeZoneWithDefault} from '@hooks';
import {createShareWorkoutLink, shareContent} from '@navigation';
import {allWorkoutTypesToDisplay, getWorkoutTypeLabel, timestampToDate, type Workout, type WorkoutType} from '@types';
import {
  formatTimestamp,
  getIsoStringFromDate,
  isEmptyArray,
  isValidLink,
  useAppTheme,
} from '@utils';
import {CopyClipboardWrapper} from '../CopyClipboardButton';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {SearchAndAddUsers} from '../Participants';
import {DuplicateWorkoutButton} from './CreateWorkoutButton';
import {WorkoutLinks} from './WorkoutLinks';
import {WorkoutStatusIcon} from './WorkoutStatusIcon';

type EditWorkoutProps = {
  hasAddParticipants: boolean;
  isFirstCreate?: true;
  isOutline?: true;
  isReadOnly?: true;
  onHasChange?: (value: boolean) => void;
  onSubmitSuccess?: () => void;
  searchQueryKey: string;
  submitText?: string;
  value: Workout;
};

// eslint-disable-next-line max-lines-per-function, complexity -- form component
export const EditWorkout: React.FC<EditWorkoutProps> = ({
  hasAddParticipants,
  isFirstCreate = false,
  isReadOnly = false,
  onHasChange,
  onSubmitSuccess,
  searchQueryKey,
  submitText,
  value,
}) => {
  const {
    isChanged,
    isLoading,
    onAddParticipant,
    onEndDateChange,
    onImagesChanged,
    onIsCompletedChange,
    onLinksChanged,
    onNameChange,
    onNotesChange,
    onRemoveParticipant,
    onStartDateChange,
    onSubmit,
    onTypeChange,
    workout,
  } = useWorkoutState(value, isFirstCreate, onSubmitSuccess);
  useEffect(() => {
    onHasChange?.(isChanged);
  }, [isChanged, onHasChange]);

  const theme = useAppTheme();
  const isTrainer = useIsCoach();
  const [hasWorkoutNameChanged, setHasWorkoutNameChanged] = useState(false);
  const [isLinksError, setIsLinksError] = useState(false);
  const copyToClipboard = useCopyToClipboard();

  const isRequiredOverrideEdits = !!workout.copyWorkoutId && isFirstCreate;
  const [hasDateChanged, setHasDateChanged] = useState(!isRequiredOverrideEdits);
  const setHasDateChangedTrue = useCallback(() => {
    setHasDateChanged(true);
  }, [setHasDateChanged]);
  const [hasParticipantsBeenConfirmed, setHasParticipantsBeenConfirmed] = useState(
    !(isRequiredOverrideEdits && hasAddParticipants),
  );
  const isErrorOverride = !hasDateChanged || !hasParticipantsBeenConfirmed;

  const isErrorName = !workout.workoutName;
  const isErrorParticipants = isEmptyArray(workout.participantIds);
  const isErrorType = isEmptyArray(workout.type);
  const isError =
    isErrorName || isErrorParticipants || isErrorType || isLinksError || isErrorOverride;
  const outlineStyle = isReadOnly ? {borderColor: theme.colors.readOnlyFieldBroder} : undefined;

  const startDate = useMemo(
    () => timestampToDate(workout.startedDateTime),
    [workout.startedDateTime],
  );
  const endDate = useMemo(() => timestampToDate(workout.endedDateTime), [workout.endedDateTime]);

  const isTodayOrBefore = useIsWorkoutTodayOrBefore(workout);
  const isSwitchDisabled = !isTodayOrBefore || isReadOnly;

  const timeZone = useCurrentTimeZoneWithDefault();

  return (
    <>
      {DEV_FEATURE_FLAGS().isDebugViewEnabled && !isFirstCreate && (
        <CopyClipboardWrapper text={workout.id}>
          <Text py={1}>Work out ID: {workout.id}</Text>
        </CopyClipboardWrapper>
      )}
      {!isFirstCreate && (
        <Box flexDirection='row' justifyContent='flex-end'>
          <DuplicateWorkoutButton
            date={getIsoStringFromDate(startDate, timeZone)}
            workout={workout}
          />
          <ButtonOutlined
            icon='share-outline'
            ml={1}
            mt={1}
            onPress={async () => {
              const shareUrl = createShareWorkoutLink(workout.id);
              await shareContent(shareUrl);
            }}
          >
            Share
          </ButtonOutlined>
        </Box>
      )}

      <Box my={1}>
        <TextInput
          error={hasWorkoutNameChanged && isErrorName}
          errorLabel='Workout name is required'
          label={`${CONTENT_CODES().WORKOUT.NAME_LABEL}*`}
          outlineStyle={outlineStyle}
          placeholder={CONTENT_CODES().WORKOUT.NAME_PLACEHOLDER}
          readOnly={isReadOnly}
          value={workout.workoutName}
          onChangeText={text => {
            setHasWorkoutNameChanged(true);
            onNameChange(text);
          }}
        />
      </Box>

      <Box mt={1}>
        <DateAndTimeComponent
          errorLabel={CONTENT_CODES().WORKOUT.OVERRIDE_DATE_ERROR_LABEL}
          isError={isErrorOverride && !hasDateChanged}
          isReadOnly={isReadOnly}
          label={CONTENT_CODES().WORKOUT.DATE_LABEL}
          value={startDate}
          onChange={onStartDateChange}
          onPress={setHasDateChangedTrue}
        />
      </Box>

      <Box flexDirection='row' mt={1}>
        <Box flex={2}>
          <DateAndTimeComponent
            isReadOnly={isReadOnly}
            label={CONTENT_CODES().WORKOUT.TIME_LABEL}
            type='time'
            value={startDate}
            onChange={onStartDateChange}
            onPress={setHasDateChangedTrue}
          />
        </Box>

        <Box flex={2} pl={1}>
          <TimePicker
            isDisabled={isReadOnly}
            label={CONTENT_CODES().WORKOUT.DURATION_LABEL}
            value={{
              hours: Math.floor((endDate.getTime() - startDate.getTime()) / 1000 / 60 / 60),
              minutes: Math.floor((endDate.getTime() - startDate.getTime()) / 1000 / 60) % 60,
            }}
            onChange={duration => {
              setHasDateChangedTrue();
              if (!duration.hours && !duration.minutes) return;
              const previousDurationMs = endDate.getTime() - startDate.getTime();
              const endDateMinutes = Math.floor(previousDurationMs / 1000 / 60);
              const endDateHours = Math.floor(endDateMinutes / 60);
              const endDateMinutesComponent = endDateMinutes % 60; // Just the minutes part
              const newEndDateHoursMs = (duration.hours ?? endDateHours) * 60 * 60 * 1000;
              const newEndDateMinutesMs = (duration.minutes ?? endDateMinutesComponent) * 60 * 1000;
              const newDurationMs = newEndDateHoursMs + newEndDateMinutesMs;
              const newEndDate = new Date(startDate.getTime() + newDurationMs);
              onEndDateChange(newEndDate, 'time');
            }}
          />
        </Box>
      </Box>

      <Box pb={1} pt={2}>
        <MultiSelect
          isDisabled={isReadOnly}
          options={allWorkoutTypesToDisplay
            .filter(t => (isReadOnly ? workout.type.includes(t) : true))
            .map(t => ({
              label: getWorkoutTypeLabel(t),
              value: t,
            }))}
          placeholder={isEmptyArray(workout.type)
            ? 'Select a workout type'
            : 'Change workout type'}
          value={workout.type}
          onChange={item => {
            onTypeChange(item as WorkoutType[]);
          }}
        />
        {isErrorType && (
          <Text pl={1} pt={1} style={{color: theme.colors.error}}>
            {CONTENT_CODES().WORKOUT.TYPE_INVALID_LABEL}
          </Text>
        )}
      </Box>

      <Box pb={2} position='relative'>
        <TextInput
          multiline
          label={CONTENT_CODES().WORKOUT.NOTES_LABEL}
          numberOfLines={2}
          outlineStyle={outlineStyle}
          placeholder={CONTENT_CODES().WORKOUT.NOTES_PLACEHOLDER}
          readOnly={isReadOnly}
          right={null}
          style={{minHeight: 84}}
          value={workout.notes}
          onChangeText={onNotesChange}
        />
        {workout.notes && (
          <IconButton
            icon='content-copy'
            style={{position: 'absolute', right: 0, top: 15, backgroundColor: '#fff'}}
            onPress={() => copyToClipboard(workout.notes, 'notes')}
          />
        )}
      </Box>

      {hasAddParticipants && (
        <>
          <SearchAndAddUsers
            isReadOnly={isReadOnly || !isFirstCreate}
            label={`${CONTENT_CODES().WORKOUT.PARTICIPANTS_LABEL}* (trainer only)`}
            parentWorkout={isFirstCreate ? undefined : workout}
            searchQueryKey={searchQueryKey}
            userIds={workout.participantIds}
            onAdd={id => {
              onAddParticipant(id);
              setHasParticipantsBeenConfirmed(true);
            }}
            onRemove={id => {
              onRemoveParticipant(id);
              setHasParticipantsBeenConfirmed(true);
            }}
          />
          {!hasParticipantsBeenConfirmed && (
            <Box pt={1}>
              <ButtonOutlined
                icon='check-circle'
                style={{borderColor: theme.colors.error}}
                textColor={theme.colors.error}
                onPress={() => setHasParticipantsBeenConfirmed(true)}
              >
                {CONTENT_CODES().WORKOUT.OVERRIDE_PARTICIPANT_ERROR_LABEL}
              </ButtonOutlined>
            </Box>
          )}
          <Box pt={2} />
        </>
      )}

      <Switch
        disabled={isSwitchDisabled}
        label='Workout complete?'
        value={workout.isCompleted}
        valueLabelFalse={
          <>
            <WorkoutStatusIcon
              isComplete={workout.isCompleted}
              isTodayOrBefore={isTodayOrBefore}
            />
            {/* <Text pl={1} variant='labelMedium'>
                {isTodayOrBefore ? 'No 🚫' : 'Pending until start date ⏳'}
              </Text> */}
          </>
        }
        valueLabelTrue={
          <>
            <WorkoutStatusIcon
              isComplete={workout.isCompleted}
              isTodayOrBefore={isTodayOrBefore}
            />
            {/* <Text pl={1} variant='labelMedium'>Yes ✅</Text> */}
          </>
        }
        onValueChange={onIsCompletedChange}
      />

      <Box flexDirection='column' justifyContent='space-between' pt={1}>
        {isTrainer && (
          <Box pb={1}>
            <WorkoutLinks
              isInError={isLinksError}
              setIsInError={setIsLinksError}
              value={workout.links}
              valuePreview={workoutLink =>
                isValidLink(workoutLink.url) && (
                  <TextLink
                    style={{
                      verticalAlign: 'middle',
                    }}
                    onPress={() => {
                      void openURL(workoutLink.url);
                    }}
                  >
                    {workoutLink.label || workoutLink.url}
                  </TextLink>
                )}
              onChange={onLinksChanged}
            />
          </Box>
        )}

        <PhotoListInput
          addLabel='Workout Photos'
          imageProps={{contentFit: 'contain', style: {height: 150}}}
          items={workout.images}
          storagePathPrefix={DOMAIN_CONSTANTS().CLOUD_STORAGE_PATH_PREFIXES.WORKOUTS(workout.id)}
          onChange={onImagesChanged}
        />
      </Box>

      <Box pb={4} />

      {isReadOnly && (
        <Box my={2}>
          <Text variant='bodySmall'>
            {`Date and time workout created: ${formatTimestamp(workout.createdDateTime)}`}
          </Text>
        </Box>
      )}

      {submitText && isChanged && (
        <StickyAboveNavigation style={{bottom: 30}}>
          <Box mx='auto'>
            <ButtonContained
              disabled={isLoading || isError}
              icon={isFirstCreate ? 'check' : 'content-save-outline'}
              loading={isLoading}
              onPress={onSubmit}
            >
              {submitText}
            </ButtonContained>
          </Box>
        </StickyAboveNavigation>
      )}

      <Box mb={8} />
    </>
  );
};
