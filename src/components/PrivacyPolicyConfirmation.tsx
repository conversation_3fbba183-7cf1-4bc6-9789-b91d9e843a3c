import {useEffect, useState} from 'react';
import {Box, ButtonContained, ButtonOutlined, PressableButton, Text, TextLink} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAppUserUpdatePrivacyPolicy} from '@contexts';
import {useBooleanDelay, useOnMount} from '@hooks';
import {onLinkToFlyBodies, onLinkToHelp, onLinkToPrivacyPolicy} from '@navigation';
import {Images} from '@assets';

type PrivacyPolicyConfirmationProps = Record<string, unknown>;

export const PrivacyPolicyConfirmation: React.FC<PrivacyPolicyConfirmationProps> = () => {
  const {hasAcceptedPrivacyPolicy, isPendingAccept, isPendingLogout, onAccept, onDismiss} =
    useAppUserUpdatePrivacyPolicy();
  const [hasClickedLink, setHasClickedLink] = useState(Boolean(hasAcceptedPrivacyPolicy));
  const {hasBeenDelayed, startDelayTimer} = useBooleanDelay(3000);
  useOnMount(startDelayTimer);
  useEffect(() => {
    if (!hasBeenDelayed) return;
    setHasClickedLink(true);
  }, [hasBeenDelayed]);

  return (
    <Box>
      <PressableButton onPress={onLinkToFlyBodies}>
        <Images.flybodiesFBlack
          contentFit='contain'
          style={{height: 100, marginHorizontal: 'auto', width: '100%'}}
        />
      </PressableButton>

      <Text pt={2} variant='headlineMedium'>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.TITLE}</Text>
      <Box display='flex'>
        <Text pb={2} variant='titleLarge'>
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.WELCOME}
        </Text>

        <Text pb={2}>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.BODY_1}</Text>

        <ButtonOutlined
          contentStyle={{flexDirection: 'row-reverse'}}
          icon='link'
          mb={2}
          onPress={() => {
            setHasClickedLink(true);
            onLinkToPrivacyPolicy();
          }}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.VIEW_PRIVACY_POLICY_BUTTON_LABEL}
        </ButtonOutlined>

        <Text pb={2}>
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.BODY_2}
        </Text>

        <Text pb={2}>{CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.QUESTIONS}
          <TextLink onPress={onLinkToHelp}>
            {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.CONTACT_EMAIL}
          </TextLink>
        </Text>
      </Box>
      <Box flexDirection='row' justifyContent='space-between'>
        <ButtonOutlined
          contentStyle={{flexDirection: 'row-reverse'}}
          disabled={isPendingLogout || isPendingAccept}
          icon='close'
          loading={isPendingLogout}
          style={{alignSelf: 'flex-start'}}
          onPress={onDismiss}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.CANCEL_BUTTON_LABEL}
        </ButtonOutlined>
        <ButtonContained
          contentStyle={{flexDirection: 'row-reverse'}}
          disabled={!hasClickedLink || isPendingAccept || isPendingLogout}
          icon='check'
          loading={isPendingAccept}
          ml={2}
          style={{alignSelf: 'flex-start'}}
          onPress={onAccept}
        >
          {CONTENT_CODES().PRIVACY_POLICY_CONFIRMATION.ACCEPT_BUTTON_LABEL}
        </ButtonContained>
      </Box>
    </Box>
  );
};
