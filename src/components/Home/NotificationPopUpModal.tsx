import {Box, ButtonContained, ButtonOutlined, Icon, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useNotificationPopUpModalState} from '@contexts';
import {memoComponent} from '@utils';
import {ManagedModal} from '../Shared';

type NotificationPopUpModalProps = Record<string, unknown>;

export const NotificationPopUpModal: React.FC<NotificationPopUpModalProps> = memoComponent(() => {
  const {isOpen, onConfirm, onDismiss} = useNotificationPopUpModalState();

  return (
    <ManagedModal isNewBackgroundColor isOpen={isOpen} onDismiss={onDismiss}>
      <Box alignItems='center' pb={1}>
        <Icon name='bell-ring-outline' size={48} />
      </Box>

      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().EXPLANATIONS.ENABLE_NOTIFICATIONS_MODAL.TITLE}
      </Text>
      <Box py={1} />

      <Text>{CONTENT_CODES().EXPLANATIONS.ENABLE_NOTIFICATIONS_MODAL.CONTENT_1}</Text>
      <Box py={1} />

      <Box alignItems='center' flexDirection='column'>
        <ButtonContained icon='arrow-right-circle-outline' mb={2} onPress={onConfirm}>
          {CONTENT_CODES().EXPLANATIONS.ENABLE_NOTIFICATIONS_MODAL.OK_BUTTON}
        </ButtonContained>
        <ButtonOutlined onPress={onDismiss}>
          {CONTENT_CODES().EXPLANATIONS.ENABLE_NOTIFICATIONS_MODAL.CANCEL}
        </ButtonOutlined>
      </Box>
    </ManagedModal>
  );
});
