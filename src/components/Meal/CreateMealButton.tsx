import {MealIcon} from '@assets';
import {ButtonOutlined, ContextMenu} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useAppUserSafe, useCreateMealContextActions, useIsCoach} from '@contexts';
import type {IconSource, IsoDate, Meal} from '@types';
import {useAppTheme} from '@utils';
import {DashedOutlineHomeButton} from '../Shared';

type CreateMealButtonProps = {
  date: IsoDate;
  icon?: IconSource;
  label?: string;
  meal?: Meal | undefined;
};

export const CreateMealButton: React.FC<CreateMealButtonProps> = ({
  date,
  icon = 'food-apple',
  label = CONTENT_CODES().MEAL.CREATE_MEAL,
  meal,
}) => {
  const isCoach = useIsCoach();
  const appUser = useAppUserSafe();
  const {contextActions, onPress} = useCreateMealContextActions(date, meal, appUser.id);

  return (
    <>
      {isCoach && (
        <ContextMenu actions={contextActions}>
          <ButtonOutlined icon={icon} mt={1}>
            {label}
          </ButtonOutlined>
        </ContextMenu>
      )}
      {!isCoach && (
        <ButtonOutlined icon={icon} mt={1} onPress={onPress}>
          {label}
        </ButtonOutlined>
      )}
    </>
  );
};

type CreateMealButtonContainerProps = {
  date: IsoDate | undefined;
  label?: string;
  meal?: Meal | undefined;
};

export const CreateMealButtonContainer: React.FC<CreateMealButtonContainerProps> = ({
  date,
  label = CONTENT_CODES().MEAL.CREATE_MEAL,
  meal,
}) => {
  const theme = useAppTheme();
  const isCoach = useIsCoach();
  const appUser = useAppUserSafe();
  const {contextActions, onPress} = useCreateMealContextActions(date, meal, appUser.id);

  return (
    <DashedOutlineHomeButton
      backgroundColor={theme.colors.mealColor}
      contextActions={isCoach ? contextActions : undefined}
      icon={<MealIcon fill='#fff' scale={0.7} />}
      label={label}
      onPress={isCoach ? undefined : onPress}
    />
  );
};
