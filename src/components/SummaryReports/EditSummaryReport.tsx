import {Box, ButtonContained, DropDownSelfContained, Grid, Text} from '@base-components';
import {CONTENT_CODES, DEV_FEATURE_FLAGS} from '@constants';
import {useEditSummaryReportState, type UseEditSummaryReportStateType} from '@contexts';
import {type CreateSummaryReportType, SummaryReportType} from '@types';
import {isNonEmptyArray} from '@utils';
import {EditSummaryReportChallenge} from './EditSummaryReportChallenge';
import {EditSummaryReportSevenDay} from './EditSummaryReportSevenDay';

const ALL_REPORT_TYPE_OPTIONS = [
  {
    label: 'No type selected',
    value: '',
  },
  {
    label: '7-day Mileage Report',
    value: SummaryReportType.SEVENDAY,
  },
  {
    label: 'Challenge Mileage Report',
    value: SummaryReportType.CHALLENGE,
  },
];

type EditSummaryReportProps = {
  initialState: CreateSummaryReportType;
  onSubmitSuccess: () => void;
};

export const EditSummaryReport: React.FC<EditSummaryReportProps> = ({
  initialState,
  onSubmitSuccess,
}) => {
  const editReportState = useEditSummaryReportState(initialState, onSubmitSuccess);

  const isValidSevenDay =
    editReportState.summaryReport.type === SummaryReportType.SEVENDAY &&
    isNonEmptyArray(editReportState.summaryReport.userIds) &&
    !!editReportState.summaryReport.creatorOrganizationId;

  const isValidChallenge =
    editReportState.summaryReport.type === SummaryReportType.CHALLENGE &&
    !!editReportState.summaryReport.challengeId &&
    !!editReportState.summaryReport.creatorOrganizationId;

  const isValidToSubmit = isValidSevenDay || isValidChallenge;

  return (
    <>
      {DEV_FEATURE_FLAGS().isDebugViewEnabled && (
        <Text pb={2}>{JSON.stringify(editReportState.summaryReport)}</Text>
      )}

      <Text variant='bodyMedium'>{CONTENT_CODES().SUMMARY_REPORTS.EXPLANATION}</Text>

      <Box pb={2} />

      <DropDownSelfContained
        label={`${CONTENT_CODES().SUMMARY_REPORTS.TYPE_LABEL}*`}
        options={ALL_REPORT_TYPE_OPTIONS}
        value={editReportState.summaryReport.type}
        onChange={editReportState.onEditType as (value: string) => void}
      />

      <Box pb={2} />

      {!!editReportState.summaryReport.type && (
        <>
          {editReportState.summaryReport.type === SummaryReportType.SEVENDAY && (
            <EditSummaryReportSevenDay
              state={editReportState as UseEditSummaryReportStateType<SummaryReportType.SEVENDAY>}
            />
          )}

          {editReportState.summaryReport.type === SummaryReportType.CHALLENGE && (
            <EditSummaryReportChallenge
              state={editReportState as UseEditSummaryReportStateType<SummaryReportType.CHALLENGE>}
            />
          )}

          <Box pb={2} />

          <Text variant='bodySmall'>{CONTENT_CODES().SUMMARY_REPORTS.WARNING_DESCRIPTION}</Text>

          <Box pb={4} />

          <Grid item pb={2} xs={12}>
            <ButtonContained
              disabled={!isValidToSubmit || editReportState.isLoading}
              icon='email-send-outline'
              loading={editReportState.isLoading}
              onPress={editReportState.onSubmit}
            >
              {CONTENT_CODES().SUMMARY_REPORTS.CREATE_BUTTON_LABEL}
            </ButtonContained>
          </Grid>
        </>
      )}

      <Box pb={4} />
    </>
  );
};
