import {Box, ButtonContained, ButtonOutlined, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {getDynamicContent, useChallengeMilestonePopUpModalState} from '@contexts';
import {useLinkTo} from '@navigation';
import {type AppUser, type Challenge} from '@types';
import {ManagedModal} from '../Shared';

type ChallengeMilestonePopUpModalProps = {
  appUser: AppUser;
  challenge: Challenge;
};

export const ChallengeMilestonePopUpModal: React.FC<ChallengeMilestonePopUpModalProps> = ({
  appUser,
  challenge,
}) => {
  const participant = challenge.participants?.find(p => p.id === appUser.id);
  const {isOpen, onDismiss} = useChallengeMilestonePopUpModalState(challenge, participant);
  const dynamicContent = getDynamicContent(challenge, participant);
  const to = useLinkTo();

  const onContinue = () => {
    onDismiss();
    to.viewChallengeScreen({challengeId: challenge.id});
  };

  return (
    <ManagedModal
      isNewBackgroundColor
      isOpen={isOpen}
      onDismiss={onDismiss}
    >
      <Text variant='headlineMedium'>{CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.TITLE}</Text>
      <Box py={1} />
      <Text>{dynamicContent}</Text>
      <Box py={1} />
      <Text>{CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.BODY_CONTENT}</Text>

      <Box flexDirection='row' justifyContent='space-between' pt={2}>
        <ButtonOutlined onPress={onDismiss}>
          {CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.DISMISS_BUTTON}
        </ButtonOutlined>
        <ButtonContained icon='arrow-right-circle-outline' onPress={onContinue}>
          {CONTENT_CODES().CHALLENGE.PROGRESS_MODAL.CONTINUE_BUTTON}
        </ButtonContained>
      </Box>
    </ManagedModal>
  );
};
