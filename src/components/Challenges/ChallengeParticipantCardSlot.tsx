import {Fragment, useId} from 'react';
import {Box, ButtonContained, Text, TouchableHighlight} from '@base-components';
import {useChallengeInviteCode} from '@contexts';
import {useShareContentMutation} from '@navigation';
import type {InviteCodeChallengePayload} from '@types';
import {onHapticSelection, useAppTheme} from '@utils';

type ChallengeParticipantCardSlotProps = Record<string, unknown>;

const ChallengeParticipantCardSlot: React.FC<ChallengeParticipantCardSlotProps> = () => {
  const theme = useAppTheme();

  return (
    <TouchableHighlight
      style={{marginBottom: 8}}
      onPress={() => {
        void onHapticSelection();
      }}
    >
      <Box
        style={{
          borderWidth: 1,
          borderRadius: 10,
          borderColor: theme.colors.cardBorderColor,
          borderStyle: 'dashed',
          paddingHorizontal: 16,
          paddingVertical: 8,
          backgroundColor: '#fff',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 85,
        }}
      >
        <Text pr={1} style={{color: theme.colors.labelGray}} variant='labelMedium'>
          Open slot
        </Text>
      </Box>
    </TouchableHighlight>
  );
};

type ChallengeParticipantSlotListProps = {
  payload: InviteCodeChallengePayload;
  slots?: number | undefined;
};

export const ChallengeParticipantSlotList: React.FC<ChallengeParticipantSlotListProps> = ({
  payload,
  slots,
}) => {
  // Only pass in payload if there are slots available
  const id = useId();
  const hasSlots = slots !== undefined && slots > 0;
  const {data, isPending} = useChallengeInviteCode(hasSlots ? payload : undefined);
  const shareMutation = useShareContentMutation();
  const isLoading = isPending || shareMutation.isPending;

  const slotElement = <ChallengeParticipantCardSlot />;

  return (
    <>
      {hasSlots &&
        Array.from({length: slots}).map((_, index) => (
          // eslint-disable-next-line react/no-array-index-key -- keys won't change
          <Fragment key={`slot-${id}-${index}`}>{slotElement}</Fragment>
        ))}
      <ButtonContained
        disabled={slots === 0}
        icon='plus'
        loading={hasSlots && isLoading}
        onPress={async () => {
          if (!data || (isPending as boolean)) return;
          await shareMutation.mutateAsync(data.url);
        }}
      >
        Invite teammate
      </ButtonContained>
    </>
  );
};
