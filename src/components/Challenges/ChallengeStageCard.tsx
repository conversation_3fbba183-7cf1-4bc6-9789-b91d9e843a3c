import {useCallback, useMemo, useState} from 'react';
import {Box, ButtonOutlined, Grid, Icon, IconButton, Switch, Text, TextInput} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  type ChallengeStage,
  dateToRawTimestamp,
  type RawTimestamp,
  timestampToDate,
  type TimeZones,
  type UUIDString,
} from '@types';
import {useAppTheme} from '@utils';
import {DateAndTimeComponent} from '../DateAndTimeComponent';
import {ManagedModal} from '../Shared';
import {ChallengeStageColorText} from './ChallengeStageColorText';

type ChallengeStageCardProps = {
  challengeEndDate: Date;
  challengeStartDate: Date;
  onRemove: (stageId: UUIDString) => void;
  onUpdateStageName: (stageId: UUIDString, name: string) => void;
  onUpdateStageTime: (
    stageId: UUIDString,
    startedDateTime?: RawTimestamp,
    endedDateTime?: RawTimestamp,
  ) => void;
  stage: ChallengeStage;
  timeZone: TimeZones;
};

export const CHALLENGE_CARD_ITEM_SIZE = 60;

// eslint-disable-next-line max-lines-per-function -- allowed because nested modal
export const ChallengeStageCard: React.FC<ChallengeStageCardProps> = ({
  challengeEndDate,
  challengeStartDate,
  onRemove,
  onUpdateStageName,
  onUpdateStageTime,
  stage,
  timeZone,
}) => {
  const theme = useAppTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const startDate = useMemo(() => timestampToDate(stage.startedDateTime), [stage.startedDateTime]);
  const endDate = useMemo(() => timestampToDate(stage.endedDateTime), [stage.endedDateTime]);
  const onStartChange = useCallback(
    (date: Date) => {
      onUpdateStageTime(stage.id, dateToRawTimestamp(date), undefined);
    },
    [onUpdateStageTime, stage.id],
  );
  const onEndChange = useCallback(
    (date: Date) => {
      onUpdateStageTime(stage.id, undefined, dateToRawTimestamp(date));
    },
    [onUpdateStageTime, stage.id],
  );

  return (
    <>
      <Grid
        container
        alignItems='center'
        flexDirection='row'
        flexWrap='wrap'
        justifyContent='space-between'
      >
        <Grid container item alignItems='center' flexDirection='row' flexWrap='wrap' xs={8}>
          <ChallengeStageColorText pr={1} stage={stage} />
          <ChallengeStageColorText
            disableColor
            isDuration
            stage={stage}
            timeZone={timeZone}
            variant='bodyMedium'
          />
        </Grid>

        <Grid item xs={4}>
          <Box flexDirection='row' justifyContent='flex-end'>
            <IconButton
              icon='pencil'
              onPress={() => {
                setIsModalOpen(true);
              }}
            />
            <Box justifyContent='center' pl={2}>
              <Box
                style={{
                  borderWidth: 1,
                  borderRadius: 6,
                  backgroundColor: '#f1f2f4',
                  borderColor: '#f1f2f4',
                }}
              >
                <Icon name='drag-horizontal' size={24} />
              </Box>
            </Box>
          </Box>
        </Grid>
      </Grid>

      <ManagedModal
        isNewBackgroundColor
        isOpen={isModalOpen}
        onDismiss={() => setIsModalOpen(false)}
      >
        <Grid container alignItems='center' flexDirection='row' justifyContent='space-between'>
          <Grid item xs={2} />
          <Grid item xs={8}>
            <Text textAlign='center' variant='headlineMedium'>
              {CONTENT_CODES().CHALLENGE.EDIT.EDIT_STAGE}
            </Text>
          </Grid>
          <Grid item xs={2}>
            <IconButton
              icon='delete'
              iconColor={theme.colors.delete}
              mode='contained'
              onPress={() => onRemove(stage.id)}
            />
          </Grid>
        </Grid>

        <TextInput
          label={CONTENT_CODES().CHALLENGE.EDIT.STAGE_NAME_LABEL}
          placeholder={CONTENT_CODES().CHALLENGE.EDIT.STAGE_NAME_PLACEHOLDER}
          value={stage.name}
          onChangeText={text => onUpdateStageName(stage.id, text)}
        />

        <Box pb={2} />

        <DateAndTimeComponent
          label={CONTENT_CODES().CHALLENGE.EDIT.START_DATE_STAGE}
          timeZone={timeZone}
          type='date'
          validEndDate={challengeEndDate}
          validStartDate={challengeStartDate}
          value={startDate}
          onChange={onStartChange}
        />

        <Box pb={2} />

        <DateAndTimeComponent
          label={CONTENT_CODES().CHALLENGE.EDIT.END_DATE_STAGE}
          timeZone={timeZone}
          type='date'
          validEndDate={challengeEndDate}
          validStartDate={challengeStartDate}
          value={endDate}
          onChange={onEndChange}
        />

        <Box pb={2} />

        <Switch
          label='Is Primary Stage'
          value={true as boolean}
          valueLabelFalse='No'
          valueLabelTrue='Yes'
        />

        <Box pb={2} />

        <Box flexDirection='row' justifyContent='center' pt={2}>
          <ButtonOutlined onPress={() => setIsModalOpen(false)}>
            {CONTENT_CODES().CHALLENGE.EDIT.STAGE_CLOSED}
          </ButtonOutlined>
        </Box>
      </ManagedModal>
    </>
  );
};
