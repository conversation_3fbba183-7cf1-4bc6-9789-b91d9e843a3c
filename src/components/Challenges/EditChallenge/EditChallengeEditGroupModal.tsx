import {
  EditChallengeProvider,
  useChallengesCreateChallengeParams,
  useSetChallengeGroupId,
} from '@contexts';
import {ManagedModal} from '../../Shared';
import {EditChallengeGroup} from './EditChallengeGroup';

type EditChallengeEditGroupModalProps = Record<string, unknown>;

export const EditChallengeEditGroupModal: React.FC<EditChallengeEditGroupModalProps> = () => {
  const {challengeGroupId} = useChallengesCreateChallengeParams();
  const setGroupId = useSetChallengeGroupId();

  return (
    <ManagedModal
      isFullWidth
      isNewBackgroundColor
      isOpen={challengeGroupId !== undefined}
      onDismiss={() => setGroupId(undefined)}
    >
      {challengeGroupId && (
        <EditChallengeProvider>
          <EditChallengeGroup groupId={challengeGroupId} onDismiss={() => setGroupId(undefined)} />
        </EditChallengeProvider>
      )}
    </ManagedModal>
  );
};
