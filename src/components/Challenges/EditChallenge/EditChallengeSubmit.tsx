import {useMemo} from 'react';
import {Box, ButtonContained, Grid, StickyAboveNavigation, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useChallengeSubmit, useEditChallengeValue, useIsChallengeLoading} from '@contexts';
import {timestampToDate} from '@types';
import {isAllChallengeStagesValid} from '@utils';

export const EditChallengeSubmit: React.FC<{
  onSubmitSuccess: (() => void) | undefined;
  submitText: string;
}> = ({onSubmitSuccess, submitText}) => {
  const challenge = useEditChallengeValue();
  const {hasChanged, isSuccess, onSubmit} = useChallengeSubmit(onSubmitSuccess);
  const isLoading = useIsChallengeLoading();

  const startDate = useMemo(
    () => timestampToDate(challenge.startedDateTime),
    [challenge.startedDateTime],
  );
  const endDate = useMemo(
    () => timestampToDate(challenge.endedDateTime),
    [challenge.endedDateTime],
  );
  const hasDuration = startDate.getTime() !== endDate.getTime();
  const isValidStages = isAllChallengeStagesValid(challenge);

  const isError = !challenge.challengeName || !isValidStages || !hasDuration;

  return (
    <>
      <Box mt={2} />

      {isSuccess && (
        <Grid center container flexDirection='row' mb={2}>
          <Text>{CONTENT_CODES().CHALLENGE.EDIT.SUBMIT_SUCCESS}</Text>
        </Grid>
      )}

      {hasChanged && (
        <StickyAboveNavigation style={{bottom: 30}}>
          <Box mx='auto'>
            <ButtonContained
              disabled={isLoading || isError}
              icon='check'
              loading={isLoading}
              onPress={onSubmit}
            >
              {submitText}
            </ButtonContained>
          </Box>
        </StickyAboveNavigation>
      )}

      <Box mt={2} />
    </>
  );
};
