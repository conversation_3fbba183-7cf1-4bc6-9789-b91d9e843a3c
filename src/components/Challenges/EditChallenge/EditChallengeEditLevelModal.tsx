import {
  EditChallengeProvider,
  useChallengesCreateChallengeParams,
  useSetChallengeLevel,
} from '@contexts';
import {ManagedModal} from '../../Shared';
import {EditChallengeLevel} from './EditChallengeLevel';

type EditChallengeEditLevelModalProps = Record<string, unknown>;

export const EditChallengeEditLevelModal: React.FC<EditChallengeEditLevelModalProps> = () => {
  const {challengeLevel} = useChallengesCreateChallengeParams();
  const setLevel = useSetChallengeLevel();

  return (
    <ManagedModal
      isFullWidth
      isNewBackgroundColor
      isOpen={challengeLevel !== undefined}
      onDismiss={() => setLevel(undefined)}
    >
      {challengeLevel && (
        <EditChallengeProvider>
          <EditChallengeLevel level={challengeLevel} onDismiss={() => setLevel(undefined)} />
        </EditChallengeProvider>
      )}
    </ManagedModal>
  );
};
