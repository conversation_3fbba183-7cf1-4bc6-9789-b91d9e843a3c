import {useMemo, useState} from 'react';
import {Images} from '@assets';
import {
  Box,
  ButtonOutlined,
  DropDownSelfContained,
  Grid,
  Icon,
  List,
  RadioButton,
  Switch,
  Text,
  TextInput,
} from '@base-components';
import {CONTENT_CODES, DOMAIN_CONSTANTS} from '@constants';
import {
  useAddStage,
  useChallengeName,
  useEditChallengeValue,
  useOnEndDateChange,
  useOnReOrderStages,
  useOnStartDateChange,
  useOnTimeZoneChange,
  useOnTypeChange,
  useRemoveStage,
  useSetBannerImage,
  useSetIsChallengeRolesEnabled,
  useSetMaxTeamSize,
  useToggleDraftStateOff,
  useUpdateStageName,
  useUpdateStageTime,
} from '@contexts';
import {
  allSupportedChallengeTypes,
  ChallengeGroupingType,
  ChallengeType,
  getChallengeTypeFriendly,
  getChallengeTypeIcon,
  getTimeZoneAbbreviation,
  patternMatch,
  SUPPORTED_TIMEZONES,
  timestampToDate,
} from '@types';
import {getChallengeDurationFormatted, isAllChallengeStagesValid, useAppTheme} from '@utils';
import {DateAndTimeComponent} from '../../DateAndTimeComponent';
import {ImagePickerInput} from '../../ImagePicker';
import {EditChallengeStages} from './EditChallengeStages';

const TIME_ZONE_OPTIONS = SUPPORTED_TIMEZONES.map(t => ({
  label: `${t}, ${getTimeZoneAbbreviation(t)}`,
  value: t,
}));

const MAX_TEAM_SIZE_OPTIONS = ['', 2, 3, 4, 5, 6, 7, 8, 9, 10].map(t =>
  t
    ? {
        label: t.toString(),
        value: t.toString(),
      }
    : {
        label: 'No limit',
        value: '',
      },
);

// eslint-disable-next-line max-lines-per-function -- form component
export const EditChallengeMetadata: React.FC<{isFirstCreate: boolean | undefined}> = ({
  isFirstCreate: _,
}) => {
  const theme = useAppTheme();
  const challenge = useEditChallengeValue();
  const onNameChange = useChallengeName();
  const onTimeZoneChange = useOnTimeZoneChange();
  const onStartDateChange = useOnStartDateChange();
  const onEndDateChange = useOnEndDateChange();
  const onChallengeTypeChange = useOnTypeChange();
  const onAddStage = useAddStage();
  const onRemoveStage = useRemoveStage();
  const onUpdateStageName = useUpdateStageName();
  const onUpdateStageTime = useUpdateStageTime();
  const onReorderStages = useOnReOrderStages();
  const onToggleDraftState = useToggleDraftStateOff();
  const onIsChallengeRolesEnabled = useSetIsChallengeRolesEnabled();
  const onSetMaxTeamSize = useSetMaxTeamSize();
  const [initialBannerImageUrl] = useState(challenge.bannerImageUrl);
  const {isImagePending, onChangeBannerImage} = useSetBannerImage();
  const isInitialBannerImage = challenge.bannerImageUrl === initialBannerImageUrl;

  const timeZone = challenge.timeZone ?? DOMAIN_CONSTANTS().DEFAULT_TIME_ZONE;
  const startDate = useMemo(
    () => timestampToDate(challenge.startedDateTime),
    [challenge.startedDateTime],
  );
  const endDate = useMemo(
    () => timestampToDate(challenge.endedDateTime),
    [challenge.endedDateTime],
  );
  const hasDuration = startDate.getTime() !== endDate.getTime();
  const isValidStages = isAllChallengeStagesValid(challenge);

  return (
    <>
      <Box flexDirection='row'>
        <Text>ID: </Text>
        <Text selectable>{challenge.id}</Text>
      </Box>

      <TextInput
        label={CONTENT_CODES().CHALLENGE.EDIT.CHALLENGE_NAME_LABEL}
        my={1}
        placeholder={CONTENT_CODES().CHALLENGE.EDIT.CHALLENGE_NAME_PLACEHOLDER}
        value={challenge.challengeName}
        onChangeText={onNameChange}
      />

      <Box alignItems='center' flexDirection='row' pb={1}>
        {patternMatch(challenge.groupingType)
          .with(ChallengeGroupingType.TEAMS, () => (
            <>
              <Images.defaultTeam style={{width: 28, height: 28, borderRadius: 28}} />
              <Text pl={1} variant='bodyLarge'>
                Teams Challenge
              </Text>
            </>
          ))
          .with(ChallengeGroupingType.INDIVIDUAL, () => (
            <>
              <Images.defaultProfile style={{width: 28, height: 28, borderRadius: 28}} />
              <Text pl={1} variant='bodyLarge'>
                Individual Challenge
              </Text>
            </>
          ))
          .with(ChallengeGroupingType.GROUPS, () => (
            <>
              <Images.defaultTeam style={{width: 28, height: 28, borderRadius: 28}} />
              <Text pl={1} variant='bodyLarge'>
                Groups Challenge
              </Text>
            </>
          ))
          .exhaustive()}
      </Box>

      {challenge.flags?.isDraft && (
        <>
          <Switch
            label={CONTENT_CODES().CHALLENGE.EDIT.IS_DRAFT_STATE}
            value={challenge.flags.isDraft}
            valueLabelFalse='No'
            valueLabelTrue='Yes'
            onValueChange={onToggleDraftState}
          />

          <Box pb={2} />
        </>
      )}

      <DropDownSelfContained
        label='Time zone'
        options={TIME_ZONE_OPTIONS}
        value={timeZone}
        onChange={onTimeZoneChange}
      />

      <Grid container mt={2}>
        <Grid item xs={7}>
          <DateAndTimeComponent
            label={CONTENT_CODES().CHALLENGE.EDIT.START_DATE_LABEL}
            timeZone={timeZone}
            type='date'
            value={startDate}
            onChange={onStartDateChange}
          />
        </Grid>
        <Grid item pl={1} xs={5}>
          <DateAndTimeComponent
            label={CONTENT_CODES().CHALLENGE.EDIT.START_TIME_LABEL}
            timeZone={timeZone}
            type='time'
            value={startDate}
            onChange={onStartDateChange}
          />
        </Grid>
      </Grid>

      <Grid container mt={2}>
        <Grid item xs={7}>
          <DateAndTimeComponent
            label={CONTENT_CODES().CHALLENGE.EDIT.END_DATE_LABEL}
            timeZone={timeZone}
            type='date'
            validStartDate={startDate}
            value={endDate}
            onChange={onEndDateChange}
          />
        </Grid>
        <Grid item pl={1} xs={5}>
          <DateAndTimeComponent
            label={CONTENT_CODES().CHALLENGE.EDIT.END_TIME_LABEL}
            timeZone={timeZone}
            type='time'
            value={endDate}
            onChange={onEndDateChange}
          />
        </Grid>
      </Grid>

      <Box pb={1} />

      <Box flexDirection='row' flexWrap='wrap'>
        <Text variant='labelMedium'>
          {CONTENT_CODES().CHALLENGE.EDIT.DURATION_LABEL}
        </Text>
        <Text>
          {getChallengeDurationFormatted(challenge)}
        </Text>
      </Box>
      {!hasDuration && (
        <Text pt={1} style={{color: theme.colors.error}} variant='labelMedium'>
          {CONTENT_CODES().CHALLENGE.EDIT.DURRATION_ERROR}
        </Text>
      )}

      <Box pb={1} />

      <List.Section
        title={CONTENT_CODES().CHALLENGE.EDIT.TYPE_LABEL}
        titleStyle={{paddingVertical: 0, paddingHorizontal: 0, ...theme.fonts.labelMedium}}
      >
        {allSupportedChallengeTypes.map(c => (
          <RadioButton
            key={`ChallengeType-${c}`}
            isFilled={challenge.type === c}
            label={
              <Box alignItems='center' flexDirection='row'>
                <Icon name={getChallengeTypeIcon(c)} size={30} />
                <Text pl={1} variant='labelMedium'>{getChallengeTypeFriendly(c)}</Text>
              </Box>
            }
            value={c}
            onPress={() => onChallengeTypeChange(c)}
          />
        ))}
      </List.Section>

      <Box pb={1}>
        <ImagePickerInput
          imageComponent={patternMatch(challenge.type)
            .with(ChallengeType.DISTANCE, () => Images.mileageChallengeBanner)
            .with(ChallengeType.STEP, () => Images.stepChallengeBanner)
            .with(ChallengeType.OTHER, () => Images.mileageChallengeBanner)
            .exhaustive()}
          imageStyle={{width: '100%', height: 186, borderRadius: 10, marginBottom: 8}}
          isInitialImage={isInitialBannerImage}
          isLoading={isImagePending}
          label={CONTENT_CODES().CHALLENGE.EDIT.BANNER}
          value={challenge.bannerImageUrl}
          onImageChange={onChangeBannerImage}
        />
      </Box>

      <Switch
        label={CONTENT_CODES().CHALLENGE.EDIT.ARE_CHALLENGE_ROLES_ENABLED}
        value={challenge.flags?.isChallengeRolesEnabled}
        valueLabelFalse='No'
        valueLabelTrue='Yes'
        onValueChange={onIsChallengeRolesEnabled}
      />

      <Box pb={1} />

      <DropDownSelfContained
        label={CONTENT_CODES().CHALLENGE.EDIT.MAX_TEAM_SIZE_LABEL}
        options={MAX_TEAM_SIZE_OPTIONS}
        value={String(challenge.maxTeamSize ?? '')}
        onChange={v => onSetMaxTeamSize(v ? Number(v) : undefined)}
      />

      <Box pb={3} />

      <Box alignItems='center' flexDirection='row' justifyContent='space-between' width='100%'>
        <Text variant='titleSmall'>{CONTENT_CODES().CHALLENGE.EDIT.MANAGE_STAGES}</Text>
        <ButtonOutlined disabled={!hasDuration} icon='plus' onPress={onAddStage}>
          {CONTENT_CODES().CHALLENGE.EDIT.ADD_STAGE}
        </ButtonOutlined>
      </Box>

      {!isValidStages && (
        <Text style={{color: theme.colors.error}}>
          {CONTENT_CODES().CHALLENGE.EDIT.INVALID_STAGES}
        </Text>
      )}
      <EditChallengeStages
        challenge={challenge}
        onRemove={onRemoveStage}
        onReorderStages={onReorderStages}
        onUpdateStageName={onUpdateStageName}
        onUpdateStageTime={onUpdateStageTime}
      />

      <Box mt={2} />
    </>
  );
};
