import {Box, ButtonContained, TextInput} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useChallengeParticipantsUserRoleMenuProps,
  useChallengesCreateChallengeParams,
  useEditChallengeValue,
  useSetChallengeTeamId,
} from '@contexts';
import type {ParticipantsChallenge, UUIDString} from '@types';
import {ProfileImagePickerInput} from '../../ImagePicker';
import {SearchAndAddUsers} from '../../Participants';
import {ManagedModal} from '../../Shared';

type EditChallengeTeamProps = {
  initialChallenge: ParticipantsChallenge;
  onAddOrUpdateParticipant: (userId: UUIDString, teamId?: UUIDString) => void;
  onChangeTeamName: (teamId: UUIDString, name: string) => void;
  onChangeTeamPicture: (teamId: UUIDString) => void;
  onRemoveParticipant: (userId: UUIDString) => void;
  searchQueryKey: string;
};

export const EditChallengeTeam: React.FC<EditChallengeTeamProps> = ({
  initialChallenge,
  onAddOrUpdateParticipant,
  onChangeTeamName,
  onChangeTeamPicture,
  onRemoveParticipant,
  searchQueryKey,
}) => {
  const challenge = useEditChallengeValue<ParticipantsChallenge>();
  const {teamId: teamIdParam} = useChallengesCreateChallengeParams();
  const setChallengeTeamId = useSetChallengeTeamId();
  const challengeTeam = teamIdParam && challenge.teams?.find(team => team.id === teamIdParam);
  const initialChallengeTeam = initialChallenge.teams?.find(team => team.id === teamIdParam);
  const {getUserRoleMenuProps, itemSize} = useChallengeParticipantsUserRoleMenuProps(
    challenge.participants,
  );

  return (
    <ManagedModal
      isNewBackgroundColor
      isOpen={!!challengeTeam}
      onDismiss={() => setChallengeTeamId(undefined)}
    >
      {!!challengeTeam && (
        <Box minWidth='100%'>
          <TextInput
            autoFocus={!challengeTeam.name}
            label={CONTENT_CODES().CHALLENGE.EDIT.TEAM_NAME_LABEL}
            placeholder={CONTENT_CODES().CHALLENGE.EDIT.TEAM_NAME_PLACEHOLDER}
            value={challengeTeam.name}
            onChangeText={text => onChangeTeamName(challengeTeam.id, text)}
          />

          <Box mt={2} />

          <ProfileImagePickerInput
            isInitialImage={challengeTeam.teamPicture === initialChallengeTeam?.teamPicture}
            label={CONTENT_CODES().CHALLENGE.EDIT.TEAM_PICTURE_LABEL}
            value={challengeTeam.teamPicture}
            onImageChange={_ => {
              onChangeTeamPicture(challengeTeam.id);

              return Promise.resolve();
            }}
          />

          <Box mt={2} />

          <SearchAndAddUsers
            getUserRoleMenuProps={getUserRoleMenuProps}
            itemSize={itemSize}
            label={CONTENT_CODES().CHALLENGE.EDIT.TEAM_PARTICIPANTS_LABEL}
            searchQueryKey={searchQueryKey}
            userIds={challenge.participants
              .filter(p => p.teamId === challengeTeam.id)
              .map(p => p.id)}
            onAdd={userId => onAddOrUpdateParticipant(userId, challengeTeam.id)}
            onRemove={userId => onRemoveParticipant(userId)}
          />

          <Box mt={2} />

          <ButtonContained flexShrink={1} icon='check' onPress={() => setChallengeTeamId(undefined)}>
            {CONTENT_CODES().CHALLENGE.EDIT.DISMISS_TEAM}
          </ButtonContained>
        </Box>
      )}
    </ManagedModal>
  );
};
