import {useCallback, useState} from 'react';
import {Box, ButtonContained, LoaderWrapper, Text, TextInput} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useChallengeGroup, useChallengeGroupsMutation, useEditChallengeValue} from '@contexts';
import {type ChallengeGroupDocument, timestampToDate, type UUIDString} from '@types';
import {formatFullTimestamp, isDeepEqual} from '@utils';

type EditChallengeGroupInternalProps = {
  group: ChallengeGroupDocument;
  onDismiss: () => void;
};

const EditChallengeGroupInternal: React.FC<EditChallengeGroupInternalProps> = ({
  group,
  onDismiss,
}) => {
  const challenge = useEditChallengeValue();
  const [groupModified, setGroupModified] = useState(group);

  const hasChanged = !isDeepEqual(group, groupModified);

  const {isPending, mutateAsync} = useChallengeGroupsMutation(challenge.id);

  const onSubmit = useCallback(async () => {
    if (!hasChanged) return;

    await mutateAsync(groupModified);
    onDismiss();
  }, [groupModified, hasChanged, mutateAsync, onDismiss]);

  return (
    <>
      <Box pb={2}>
        <Text>{CONTENT_CODES().CHALLENGE.EDIT.GROUP_LEVEL}</Text>
        <Text variant='bodyMedium'>{group.level}</Text>
      </Box>

      <TextInput
        label={CONTENT_CODES().CHALLENGE.EDIT.EDIT_GROUP_NAME}
        placeholder={CONTENT_CODES().CHALLENGE.EDIT.EDIT_GROUP_NAME}
        value={groupModified.name}
        onChangeText={name => setGroupModified(g => ({...g, name}))}
      />

      <Box pb={2} />

      {group.aggregationConfig.nextAggregationDateTime && (
        <Box pb={2}>
          <Text variant='bodyMedium'>
            {CONTENT_CODES().CHALLENGE.EDIT.GROUP_LAST_AGGREGATION_RUN}
          </Text>
          <Text>
            {formatFullTimestamp(timestampToDate(group.aggregationConfig.nextAggregationDateTime))}
          </Text>
        </Box>
      )}

      <Box flexDirection='row' justifyContent='center' pt={2}>
        <ButtonContained
          disabled={!hasChanged || isPending}
          icon='check'
          loading={isPending}
          onPress={onSubmit}
        >
          {CONTENT_CODES().CHALLENGE.EDIT.EDIT_GROUP_SAVE}
        </ButtonContained>
      </Box>
    </>
  );
};

type EditChallengeGroupProps = {
  groupId: UUIDString;
  onDismiss: () => void;
};

export const EditChallengeGroup: React.FC<EditChallengeGroupProps> = ({groupId, onDismiss}) => {
  const challenge = useEditChallengeValue();
  const group = useChallengeGroup(challenge.id, groupId);

  return (
    <>
      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().CHALLENGE.EDIT.EDIT_GROUP}
      </Text>

      <LoaderWrapper isLoading={!group}>
        {group && <EditChallengeGroupInternal group={group} onDismiss={onDismiss} />}
      </LoaderWrapper>
    </>
  );
};
