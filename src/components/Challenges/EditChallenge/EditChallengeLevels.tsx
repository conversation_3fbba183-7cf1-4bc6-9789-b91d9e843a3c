import {useState} from 'react';
import {Box, ButtonOutlined, ContextMenuDotsHorizontal, IconButton, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useAddLevel,
  useChallengeGroupCreateMutation,
  useEditChallengeValue,
  useEditLevelActionSheet,
} from '@contexts';
import type {GroupLevelMetadata, GroupsChallenge} from '@types';
import {EditChallengeEditGroupModal} from './EditChallengeEditGroupModal';
import {EditChallengeEditLevelModal} from './EditChallengeEditLevelModal';
import {EditChallengeGroupsList} from './EditChallengeGroupsList';

type EditChallengeLevelsProps = Record<string, unknown>;

type ChallengeLevelCardProps = {
  value: GroupLevelMetadata;
};

const ChallengeLevelCard: React.FC<ChallengeLevelCardProps> = ({value}) => {
  const challenge = useEditChallengeValue<GroupsChallenge>();
  const [isExpanded, setIsExpanded] = useState(false);
  const actions = useEditLevelActionSheet(value.level);
  const {createGroup, isPending} = useChallengeGroupCreateMutation(challenge.id, value);

  return (
    <>
      <Box
        style={{
          backgroundColor: '#fff',
          borderRadius: 10,
          padding: 16,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 8,
        }}
      >
        <Text variant='titleSmall'>
          Level {value.level} - {value.label}
        </Text>
        <Box alignItems='center' flexDirection='row'>
          <ContextMenuDotsHorizontal actions={actions} />
          <IconButton
            icon='chevron-down'
            size={32}
            style={{padding: 0, margin: 0}}
            onPress={() => setIsExpanded(isEx => !isEx)}
          />
        </Box>
      </Box>
      {isExpanded && (
        <Box pb={2}>
          <EditChallengeGroupsList indentLevel={1} level={value.level} />
          {value.level === 1 && (
            <ButtonOutlined
              disabled={isPending}
              icon='plus'
              loading={isPending}
              ml={2}
              mt={1}
              onPress={createGroup}
            >
              Add {value.groupLabel}
            </ButtonOutlined>
          )}
        </Box>
      )}
    </>
  );
};
export const EditChallengeLevels: React.FC<EditChallengeLevelsProps> = () => {
  const challenge = useEditChallengeValue<GroupsChallenge>();
  const {addLevel, isPending} = useAddLevel();

  return (
    <>
      <Box
        alignItems='center'
        flexDirection='row'
        justifyContent='space-between'
        pb={1}
        width='100%'
      >
        <Text variant='titleSmall'>{CONTENT_CODES().CHALLENGE.EDIT.MANAGE_LEVELS}</Text>
        <ButtonOutlined
          disabled={isPending}
          icon='plus'
          loading={isPending}
          onPress={addLevel}
        >
          {CONTENT_CODES().CHALLENGE.EDIT.ADD_LEVEL}
        </ButtonOutlined>
      </Box>

      {challenge.groupLevels.map(value => (
        <ChallengeLevelCard key={`challenge-level-card-${value.level}`} value={value} />
      ))}

      <EditChallengeEditLevelModal />
      <EditChallengeEditGroupModal />

      <Box pb={4} />
    </>
  );
};
