import {Box, ButtonOutlined, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useAddOrUpdateParticipant,
  useAddTeam,
  useEditChallengeValue,
  useRemoveParticipant,
  useRemoveTeam,
  useSetTeamName,
  useTeamImageChange,
} from '@contexts';
import type {TeamsChallenge} from '@types';
import {ChallengeTeamsListScrollable} from '../ChallengeTeamsList';
import {EditChallengeTeam} from './EditChallengeTeam';

export const EditChallengeTeamsAndParticipants: React.FC = () => {
  const challenge = useEditChallengeValue<TeamsChallenge>();
  const onAddOrUpdateParticipant = useAddOrUpdateParticipant();
  const onAddTeam = useAddTeam();
  const onChangeTeamName = useSetTeamName();
  const {onChangeTeamPicture} = useTeamImageChange();
  const onRemoveParticipant = useRemoveParticipant();
  const onRemoveTeam = useRemoveTeam();

  return (
    <>
      <EditChallengeTeam
        initialChallenge={challenge}
        searchQueryKey='userSearchTerm'
        onAddOrUpdateParticipant={onAddOrUpdateParticipant}
        onChangeTeamName={onChangeTeamName}
        onChangeTeamPicture={onChangeTeamPicture}
        onRemoveParticipant={onRemoveParticipant}
      />

      <Box alignItems='center' flexDirection='row' justifyContent='space-between'>
        <Text variant='titleSmall'>{CONTENT_CODES().CHALLENGE.EDIT.MANAGE_TEAMS_LABEL}</Text>
        <ButtonOutlined icon='check' onPress={onAddTeam}>
          {CONTENT_CODES().CHALLENGE.EDIT.CREATE_TEAM}
        </ButtonOutlined>
      </Box>

      <Box mx={-2}>
        <ChallengeTeamsListScrollable
          challengeId={challenge.id}
          dailyData={challenge.dailyData}
          participants={challenge.participants}
          selectedStage={undefined}
          stages={challenge.stages}
          teams={challenge.teams}
          type={challenge.type}
          onRemoveTeam={onRemoveTeam}
        />
      </Box>
    </>
  );
};
