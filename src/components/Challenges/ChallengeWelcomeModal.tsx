import {Box, ButtonContained, ButtonOutlined, Icon, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useChallengeWelcomeModalState} from '@contexts';
import {memoComponent} from '@utils';
import {ManagedModal} from '../Shared';

type ChallengeWelcomeModalProps = Record<string, unknown>;

export const ChallengeWelcomeModal: React.FC<ChallengeWelcomeModalProps> = memoComponent(() => {
  const {isChallengeWelcomeModalOpen, onConfirm, onDismiss} = useChallengeWelcomeModalState();

  return (
    <ManagedModal
      isNewBackgroundColor
      isOpen={isChallengeWelcomeModalOpen}
      onDismiss={onDismiss}
    >
      <Box alignItems='center' pb={1}>
        <Icon name='trophy-outline' size={48} />
      </Box>

      <Text textAlign='center' variant='headlineMedium'>
        {CONTENT_CODES().EXPLANATIONS.ENABLE_CHALLENGE_NOTIFICATIONS_MODAL.TITLE}
      </Text>
      <Box py={1} />

      <Text>{CONTENT_CODES().EXPLANATIONS.ENABLE_CHALLENGE_NOTIFICATIONS_MODAL.CONTENT_1}</Text>
      <Box py={1} />

      <Text>{CONTENT_CODES().EXPLANATIONS.ENABLE_CHALLENGE_NOTIFICATIONS_MODAL.CONTENT_2}</Text>

      <Box alignItems='center' pt={2}>
        <ButtonContained icon='bell-ring-outline' mb={2} onPress={onConfirm}>
          {CONTENT_CODES().EXPLANATIONS.ENABLE_CHALLENGE_NOTIFICATIONS_MODAL.CONFIRM_BUTTON}
        </ButtonContained>
        <ButtonOutlined onPress={onDismiss}>
          {CONTENT_CODES().EXPLANATIONS.ENABLE_CHALLENGE_NOTIFICATIONS_MODAL.CANCEL}
        </ButtonOutlined>
      </Box>
    </ManagedModal>
  );
});
