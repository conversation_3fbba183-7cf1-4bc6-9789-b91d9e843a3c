import {Box, Button, Text} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {
  useAppUserChallengeGroupParticipant,
  useAppUserSafe,
  useChallengeAcceptAction,
  useChallengeRejectAction,
  useIsChallengePending,
} from '@contexts';
import type {Challenge} from '@types';
import {useAppTheme} from '@utils';

type ChallengeAcceptRejectProps = {
  challenge: Challenge;
  hasHeading?: boolean;
  hasPb?: boolean;
};

export const ChallengeAcceptReject: React.FC<ChallengeAcceptRejectProps> = ({
  challenge,
  hasHeading,
  hasPb,
}) => {
  const theme = useAppTheme();
  const {id} = useAppUserSafe();
  const challengeGroupParticipant = useAppUserChallengeGroupParticipant(challenge, id);
  const {isPending: isAcceptLoading, mutateAsync: accept} = useChallengeAcceptAction(
    challenge,
    challengeGroupParticipant,
  );
  const {isPending: isRejectLoading, mutate: reject} = useChallengeRejectAction(
    challenge,
    challengeGroupParticipant,
  );
  const isChallengePending = useIsChallengePending(challenge, challengeGroupParticipant);
  const isLoading = isAcceptLoading || isRejectLoading;

  if (!isChallengePending) return null;

  return (
    <>
      {hasHeading && (
        <Text mx='auto' style={{fontSize: 24}} variant='headlineMedium'>
          {CONTENT_CODES().CHALLENGE.INVITE.HEADER}
        </Text>
      )}
      <Box flexDirection='row' justifyContent='center' pt={1} width='100%'>
        <Button
          disabled={isLoading}
          icon='close-circle-outline'
          loading={isRejectLoading}
          ml={1}
          mode='text'
          textColor={theme.actions.denyColor}
          onPress={() => reject()}
        >
          {CONTENT_CODES().CHALLENGE.INVITE.REJECT}
        </Button>
        <Button
          disabled={isLoading}
          icon='check-circle-outline'
          loading={isAcceptLoading}
          mode='text'
          textColor={theme.actions.confirmColor}
          onPress={() => accept()}
        >
          {CONTENT_CODES().CHALLENGE.INVITE.ACCEPT}
        </Button>
      </Box>
      <Box pb={hasPb ? 2 : 0} />
    </>
  );
};
