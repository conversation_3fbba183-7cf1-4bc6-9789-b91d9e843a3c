import {openInbox} from 'react-native-email-link';
import {
  Box,
  ButtonContained,
  ButtonOutlined,
  SegmentedButtons,
  Text,
  TextInput,
  TextLink,
  TouchableHighlight,
} from '@base-components';
import {CONTENT_CODES} from '@constants';
import {useLoginState} from '@contexts';
import {useLinkTo} from '@navigation';
import {isDigitsOnly, useAppTheme} from '@utils';

// eslint-disable-next-line max-lines-per-function, complexity -- shared phone and email logic for central control of auth
export const LoginComponent: React.FC = () => {
  const theme = useAppTheme();
  const to = useLinkTo();

  const {
    code,
    confirmCode,
    credentials,
    errorEmail,
    errorPhone,
    errorSubmit,
    isLoading,
    isLoggingIn,
    isShowEmailOpenButton,
    isShowPhoneCode,
    isValid,
    loginType,
    onChangeEmail,
    onChangePhone,
    onSubmit,
    setCode,
    setLoginType,
  } = useLoginState();

  const loggingInLoading = isLoggingIn && (
    <Box style={{paddingTop: 2}}>
      <Text style={{fontStyle: 'italic'}} variant='bodyMedium'>
        {CONTENT_CODES().AUTH.PLEASE_WAIT_LOGGING_IN}
      </Text>
    </Box>
  );

  const errorMessageSubmit = !!errorSubmit && (
    <Box style={{paddingTop: 2}}>
      <Text style={{color: theme.colors.error}} variant='bodyMedium'>
        Error: {errorSubmit}
      </Text>
    </Box>
  );

  const hasFormattingError =
    (!!errorEmail && loginType === 'email') || (!!errorPhone && loginType === 'phone');
  const errorMessageByType = hasFormattingError && (
    <Box style={{paddingTop: 2}}>
      <Text style={{color: theme.colors.error}} variant='bodyMedium'>
        {`Error: ${loginType === 'email' ? errorEmail : errorPhone}`}
      </Text>
    </Box>
  );

  const isValidCode = !!code && code.length === 6 && isDigitsOnly(code);
  const confirmPhoneCodeElement = (
    <Box minWidth='100%'>
      <Text pb={1} variant='bodyMedium'>
        {CONTENT_CODES().AUTH.CHECK_TEXTS_FOR_CODE}
      </Text>

      <TextInput
        autoFocus
        autoComplete='one-time-code'
        keyboardType='number-pad'
        placeholder='Enter verification code'
        textContentType='oneTimeCode'
        value={code}
        onChangeText={setCode}
      />

      {loggingInLoading}

      {errorMessageSubmit}

      <ButtonContained
        disabled={isLoading || isLoggingIn || !isValidCode}
        icon='lock-open-outline'
        loading={isLoading || isLoggingIn}
        mt={2}
        onPress={() => confirmCode()}
      >
        {CONTENT_CODES().AUTH.CONFIRM_CODE}
      </ButtonContained>

      <ButtonOutlined
        disabled={isLoading || isLoggingIn}
        icon='message-arrow-right-outline'
        mt={1}
        onPress={() => onSubmit()}
      >
        {CONTENT_CODES().AUTH.RESEND_CODE}
      </ButtonOutlined>
    </Box>
  );

  const loginElement = (
    <Box minWidth='100%'>
      <SegmentedButtons
        value={loginType}
        values={[
          {
            value: 'email',
            icon: 'email-outline',
            label: 'Email',
          },
          {
            value: 'phone',
            icon: 'phone-outline',
            label: 'Phone number',
          },
        ]}
        onChange={setLoginType}
      />
      <Box pb={1} />

      <TextInput
        autoComplete={loginType === 'email' ? 'email' : 'tel'}
        error={hasFormattingError}
        keyboardType={loginType === 'email' ? 'default' : 'phone-pad'}
        label={
          loginType === 'email'
            ? CONTENT_CODES().AUTH.CREDENTIAL_EMAIL_LABEL
            : CONTENT_CODES().AUTH.CREDENTIAL_PHONE_LABEL
        }
        placeholder={
          loginType === 'email'
            ? CONTENT_CODES().AUTH.EMAIL_PLACEHOLDER
            : CONTENT_CODES().AUTH.PHONE_NUMBER_PLACEHOLDER
        }
        value={(loginType === 'email' ? credentials.email : credentials.phoneNumber) ?? ''}
        onChangeText={loginType === 'email' ? onChangeEmail : onChangePhone}
      />

      {errorMessageByType}

      {loggingInLoading}

      {errorMessageSubmit}

      <ButtonContained
        disabled={isLoading || !isValid || isLoggingIn || isShowEmailOpenButton}
        icon='account'
        loading={isLoading || isLoggingIn}
        my={2}
        onPress={onSubmit}
      >
        {CONTENT_CODES().AUTH.LOGIN_BUTTON}
      </ButtonContained>

      {loginType === 'email' && isShowEmailOpenButton && !isLoggingIn && (
        <Box>
          <Text pb={1}>{CONTENT_CODES().AUTH.LOGIN_MAGIC_LINK}</Text>
          <ButtonOutlined icon='email-open-outline' onPress={() => openInbox()}>
            {CONTENT_CODES().AUTH.EMAIL_OPEN_BUTTON}
          </ButtonOutlined>
        </Box>
      )}

      <Box pt={1} />

      {!isShowPhoneCode && !isShowEmailOpenButton && !isLoggingIn && (
        <TouchableHighlight onPress={() => to.signUpScreen()}>
          <Text py={1}>
            {CONTENT_CODES().AUTH.NO_ACCOUNT}{' '}
            <TextLink disableIcon>{CONTENT_CODES().AUTH.CLICK_TO_SIGN_UP}</TextLink>
          </Text>
        </TouchableHighlight>
      )}
    </Box>
  );

  return (
    <>
      <Box pb={1} pt={2}>
        <Text variant='headlineMedium'>
          {loginType === 'email'
            ? CONTENT_CODES().AUTH.LOGIN_HEADER_EMAIL
            : CONTENT_CODES().AUTH.LOGIN_HEADER_PHONE_NUMBER}
        </Text>
      </Box>
      {isShowPhoneCode ? confirmPhoneCodeElement : loginElement}
    </>
  );
};
